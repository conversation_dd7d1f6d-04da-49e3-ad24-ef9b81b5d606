<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Naroop - Narrative of Our People | Share Your Stories, Connect with Community</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Firebase Configuration -->
    <script type="module" src="/public/js/firebase-config.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .lucide {
            width: 20px;
            height: 20px;
        }
        /* Simple spinner for loading states */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4f46e5;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* Styles for the modal */
        #commentModal.hidden {
            display: none;
        }
        /* Search results styling */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        /* Loading skeleton animations */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        /* Smooth transitions */
        .transition-all {
            transition: all 0.3s ease;
        }

        /* Mobile Bottom Navigation Styles */
        .mobile-nav-item {
            transition: all 0.2s ease;
        }
        .mobile-nav-item.active {
            color: #4f46e5;
        }
        .mobile-nav-item.active .nav-icon {
            transform: scale(1.1);
        }
        .mobile-nav-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* Adjust main content for mobile bottom nav */
        @media (max-width: 768px) {
            #mainApp {
                padding-bottom: 80px;
            }

            /* Improved touch targets for mobile */
            button, .mobile-nav-item, input, textarea, select {
                min-height: 44px; /* Apple's recommended minimum touch target */
                min-width: 44px;
            }

            /* Better mobile form styling */
            input, textarea, select {
                font-size: 16px; /* Prevents zoom on iOS */
                padding: 12px 16px;
            }

            /* Mobile-optimized modals */
            .modal-content {
                margin: 10px;
                max-height: calc(100vh - 20px);
                overflow-y: auto;
            }

            /* Improved mobile cards */
            .bg-white {
                border-radius: 12px;
                margin-bottom: 16px;
            }

            /* Better mobile typography */
            h1 { font-size: 1.75rem; }
            h2 { font-size: 1.5rem; }
            h3 { font-size: 1.25rem; }

            /* Mobile-friendly spacing */
            .p-4 { padding: 1rem; }
            .p-5 { padding: 1.25rem; }
            .gap-4 { gap: 1rem; }
        }

        /* Mobile Gesture Styles */
        .pull-to-refresh {
            position: relative;
            overflow: hidden;
        }

        .pull-refresh-indicator {
            position: absolute;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            z-index: 10;
        }

        .pull-refresh-indicator.visible {
            top: 10px;
        }

        .pull-refresh-indicator.loading {
            animation: spin 1s linear infinite;
        }

        /* Swipe action styles */
        .post-swipe-container {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
        }

        .post-swipe-actions {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            display: flex;
            align-items: center;
            background: linear-gradient(90deg, #ef4444, #f97316);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1;
        }

        .post-swipe-actions.visible {
            transform: translateX(0);
        }

        .swipe-action-btn {
            padding: 0 20px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            min-width: 80px;
            transition: background-color 0.2s ease;
        }

        .swipe-action-btn:hover {
            background-color: rgba(255,255,255,0.1);
        }

        /* Touch feedback */
        .touch-feedback {
            position: relative;
            overflow: hidden;
        }

        .touch-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(79, 70, 229, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        /* Hover effects */
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Landing page specific styles */
        .bg-grid-slate-100 {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(148 163 184 / 0.05)'%3e%3cpath d='m0 .5 32 0'/%3e%3cpath d='m0 32.5 32 0'/%3e%3cpath d='m.5 0 0 32'/%3e%3cpath d='m32.5 0 0 32'/%3e%3c/svg%3e");
        }

        /* Animation for floating elements */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        /* Gradient text animation */
        @keyframes gradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .animate-gradient {
            background-size: 200% 200%;
            animation: gradient 3s ease infinite;
        }

        /* Enhanced button hover effects */
        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(148, 163, 184, 0.3);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(79, 70, 229, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* Card hover effects */
        .feature-card {
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }

        /* Form styling enhancements */
        .form-input {
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
        }

        /* Glassmorphism effect for auth cards */
        .auth-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Loading States and Spinners */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        .loading-spinner-large {
            width: 40px;
            height: 40px;
            border-width: 4px;
        }

        .loading-spinner-primary {
            border-color: rgba(79, 70, 229, 0.3);
            border-top-color: #4f46e5;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Loading overlay */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(2px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        /* Skeleton loading animation */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Button loading state */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        /* Pulse animation for loading elements */
        .pulse-loading {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Loading dots animation */
        .loading-dots::after {
            content: '';
            animation: dots 1.5s steps(4, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <div id="app" class="min-h-screen">
        <!-- Top Navigation Bar -->
        <header class="bg-white/80 backdrop-blur-lg border-b border-slate-200 fixed top-0 left-0 right-0 z-20">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <a href="#" class="flex items-center gap-2 text-xl font-bold text-slate-900">
                        <i data-lucide="layers-3"></i>
                        <span>Naroop</span>
                    </a>

                    <!-- Search Bar -->
                    <div class="hidden md:block w-full max-w-md">
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 lucide"></i>
                            <input type="text" id="searchInput" placeholder="Search stories, people, or topics..." class="w-full bg-slate-100 border border-transparent rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition">
                            <!-- Search Results Dropdown -->
                            <div id="searchResults" class="hidden absolute top-full left-0 right-0 mt-2 bg-white border border-slate-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                                <!-- Search results will appear here -->
                            </div>
                        </div>
                    </div>

                    <!-- Header Actions -->
                    <div class="flex items-center gap-2 md:gap-4">
                        <!-- Mobile Search Button -->
                        <button id="mobileSearchBtn" class="md:hidden p-2 rounded-full hover:bg-slate-100 transition">
                            <i data-lucide="search" class="text-slate-600"></i>
                        </button>

                        <button class="hidden md:flex items-center gap-2 bg-indigo-600 text-white font-semibold px-4 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">
                            <i data-lucide="pen-square"></i>
                            <span>Write</span>
                        </button>

                        <!-- Mobile Write Button -->
                        <button class="md:hidden p-2 rounded-full bg-indigo-600 text-white hover:bg-indigo-700 transition">
                            <i data-lucide="pen-square"></i>
                        </button>

                        <div class="hidden sm:block relative">
                            <button id="notificationBtn" class="p-2 rounded-full hover:bg-slate-100 transition relative">
                                <i data-lucide="bell" class="text-slate-600"></i>
                                <span id="notificationBadge" class="hidden absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">0</span>
                            </button>
                            <!-- Notification Dropdown -->
                            <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white border border-slate-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                                <div class="p-4 border-b border-slate-200 flex justify-between items-center">
                                    <h3 class="font-semibold text-slate-900">Notifications</h3>
                                    <button id="markAllReadBtn" class="text-sm text-indigo-600 hover:text-indigo-800">Mark all read</button>
                                </div>
                                <div id="notificationsList" class="divide-y divide-slate-100">
                                    <!-- Notifications will be loaded here -->
                                </div>
                            </div>
                        </div>
                        <button id="messagesBtn" class="hidden sm:block p-2 rounded-full hover:bg-slate-100 transition">
                            <i data-lucide="message-square" class="text-slate-600"></i>
                        </button>
                        <img id="userAvatar" src="https://placehold.co/40x40/E2E8F0/475569?text=JD" alt="User Avatar" class="w-8 h-8 md:w-10 md:h-10 rounded-full border-2 border-white cursor-pointer" onclick="toggleAuthModal()">
                    </div>
                </div>
            </div>
        </header>

        <!-- Landing Page View (for unauthenticated users) -->
        <div id="landingPage" class="hidden">
            <!-- Hero Section -->
            <section class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-purple-50">
                <div class="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>

                <div class="container mx-auto px-4 py-20">
                    <div class="grid lg:grid-cols-2 gap-12 items-center">
                        <!-- Left Column: Hero Content -->
                        <div class="text-center lg:text-left">
                            <div class="inline-flex items-center gap-2 bg-indigo-100 text-indigo-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                                <i data-lucide="sparkles" class="w-4 h-4"></i>
                                <span>Narrative of Our People - Celebrating Black Stories</span>
                            </div>

                            <h1 class="text-4xl md:text-6xl font-bold text-slate-900 mb-6 leading-tight">
                                Share Your
                                <span class="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
                                    Positive Stories
                                </span>
                                <br>Celebrate Our
                                <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">
                                    Community
                                </span>
                            </h1>

                            <p class="text-xl text-slate-600 mb-8 leading-relaxed">
                                A social media platform where Black people come together to share positive stories,
                                experiences, and celebrate our community. Build meaningful connections through uplifting narratives.
                            </p>

                            <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                                <button id="heroSignupBtn" class="btn-primary text-white font-semibold px-8 py-4 rounded-full">
                                    Start Your Journey
                                </button>
                                <button id="heroLoginBtn" class="btn-secondary text-slate-700 font-semibold px-8 py-4 rounded-full">
                                    Sign In
                                </button>
                            </div>
                        </div>

                        <!-- Right Column: Visual Elements -->
                        <div class="relative">
                            <!-- Floating Elements -->
                            <div class="absolute top-10 right-10 w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center animate-bounce">
                                <i data-lucide="pen-tool" class="w-8 h-8 text-purple-600"></i>
                            </div>
                            <div class="absolute bottom-10 left-10 w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center animate-pulse">
                                <i data-lucide="users" class="w-6 h-6 text-pink-600"></i>
                            </div>
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-indigo-100 rounded-full flex items-center justify-center animate-pulse">
                                <i data-lucide="heart" class="w-10 h-10 text-indigo-600"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Mission Statement Section -->
            <section class="py-20 bg-gradient-to-br from-slate-50 to-indigo-50">
                <div class="container mx-auto px-4">
                    <div class="max-w-4xl mx-auto text-center">
                        <div class="inline-flex items-center gap-2 bg-indigo-100 text-indigo-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                            <i data-lucide="heart" class="w-4 h-4"></i>
                            <span>Our Mission</span>
                        </div>

                        <h2 class="text-3xl md:text-5xl font-bold text-slate-900 mb-6 leading-tight">
                            Celebrating the
                            <span class="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
                                Narrative of Our People
                            </span>
                        </h2>

                        <p class="text-xl text-slate-600 mb-8 leading-relaxed max-w-3xl mx-auto">
                            Naroop is more than just a social platform—it's a digital sanctuary where Black voices are amplified,
                            positive stories are celebrated, and our community thrives. We believe every experience, every triumph,
                            and every moment of joy deserves to be shared and honored.
                        </p>

                        <div class="grid md:grid-cols-3 gap-8 mt-12">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="users" class="w-8 h-8 text-indigo-600"></i>
                                </div>
                                <h3 class="text-xl font-bold text-slate-900 mb-2">Community First</h3>
                                <p class="text-slate-600">Building connections that uplift and inspire our community</p>
                            </div>

                            <div class="text-center">
                                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="sparkles" class="w-8 h-8 text-purple-600"></i>
                                </div>
                                <h3 class="text-xl font-bold text-slate-900 mb-2">Positive Stories</h3>
                                <p class="text-slate-600">Focusing on experiences that inspire, motivate, and celebrate</p>
                            </div>

                            <div class="text-center">
                                <div class="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="shield-check" class="w-8 h-8 text-pink-600"></i>
                                </div>
                                <h3 class="text-xl font-bold text-slate-900 mb-2">Safe Space</h3>
                                <p class="text-slate-600">A protected environment where our voices are heard and respected</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="py-20 bg-white">
                <div class="container mx-auto px-4">
                    <div class="text-center mb-16">
                        <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                            Why Choose Naroop?
                        </h2>
                        <p class="text-xl text-slate-600 max-w-2xl mx-auto">
                            Discover the features that make sharing positive stories and building community connections effortless and engaging.
                        </p>
                    </div>

                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="feature-card text-center group p-8 rounded-2xl">
                            <div class="w-16 h-16 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300">
                                <i data-lucide="edit-3" class="w-8 h-8 text-indigo-600"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 mb-4">Share Your Journey</h3>
                            <p class="text-slate-600">
                                Create inspiring stories with our intuitive editor. Share your experiences, achievements, and positive moments that uplift our community.
                            </p>
                        </div>

                        <div class="feature-card text-center group p-8 rounded-2xl">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300">
                                <i data-lucide="users" class="w-8 h-8 text-purple-600"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 mb-4">Our Community</h3>
                            <p class="text-slate-600">
                                Connect with fellow community members, follow inspiring voices, and build meaningful relationships through shared positive experiences.
                            </p>
                        </div>

                        <div class="feature-card text-center group p-8 rounded-2xl">
                            <div class="w-16 h-16 bg-gradient-to-br from-pink-100 to-pink-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300">
                                <i data-lucide="trending-up" class="w-8 h-8 text-pink-600"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 mb-4">Inspire & Be Inspired</h3>
                            <p class="text-slate-600">
                                Discover uplifting stories from our community. Our curated feed highlights positive experiences that motivate and celebrate our people.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Authentication Section -->
            <section class="py-20 bg-slate-50">
                <div class="container mx-auto px-4">
                    <div class="max-w-4xl mx-auto">
                        <div class="text-center mb-12">
                            <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                                Ready to Join Our Community?
                            </h2>
                            <p class="text-xl text-slate-600">
                                Become part of Naroop and start sharing your positive stories with our community.
                            </p>
                        </div>

                        <div class="grid md:grid-cols-2 gap-8">
                            <!-- Sign Up Form -->
                            <div class="auth-card p-8 rounded-2xl">
                                <div class="text-center mb-6">
                                    <div class="w-16 h-16 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i data-lucide="user-plus" class="w-8 h-8 text-indigo-600"></i>
                                    </div>
                                    <h3 class="text-2xl font-bold text-slate-900">Join Naroop</h3>
                                    <p class="text-slate-600 mt-2">Become part of our community celebrating Black stories and experiences</p>
                                </div>
                                <form id="landingSignupForm" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Username</label>
                                        <input type="text" id="landingSignupUsername" class="form-input w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-3" placeholder="Choose a username" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Email</label>
                                        <input type="email" id="landingSignupEmail" class="form-input w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-3" placeholder="<EMAIL>" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Password</label>
                                        <input type="password" id="landingSignupPassword" class="form-input w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-3" placeholder="Create a strong password" required>
                                        <div id="landingPasswordStrength"></div>
                                    </div>
                                    <div id="landingSignupError" class="text-sm text-red-500"></div>
                                    <button type="submit" class="btn-primary w-full text-white font-semibold py-3 rounded-lg">
                                        Create Account
                                    </button>
                                </form>
                                <p class="text-center text-sm text-slate-500 mt-4">
                                    Already have an account?
                                    <button id="switchToLogin" class="text-indigo-600 hover:text-indigo-800 font-medium transition-colors">Sign in here</button>
                                </p>
                            </div>

                            <!-- Sign In Form -->
                            <div class="auth-card p-8 rounded-2xl">
                                <div class="text-center mb-6">
                                    <div class="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i data-lucide="log-in" class="w-8 h-8 text-slate-600"></i>
                                    </div>
                                    <h3 class="text-2xl font-bold text-slate-900">Welcome Back to Naroop</h3>
                                    <p class="text-slate-600 mt-2">Continue sharing your positive stories with our community</p>
                                </div>
                                <form id="landingLoginForm" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Email</label>
                                        <input type="email" id="landingLoginEmail" class="form-input w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-3" placeholder="<EMAIL>" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Password</label>
                                        <input type="password" id="landingLoginPassword" class="form-input w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-3" placeholder="Enter your password" required>
                                    </div>
                                    <div id="landingLoginError" class="text-sm text-red-500"></div>
                                    <button type="submit" class="w-full bg-slate-900 text-white font-semibold py-3 rounded-lg hover:bg-slate-800 transition-all duration-300 transform hover:scale-105">
                                        Sign In
                                    </button>
                                </form>
                                <p class="text-center text-sm text-slate-500 mt-4">
                                    New to Narrate?
                                    <button id="switchToSignup" class="text-indigo-600 hover:text-indigo-800 font-medium transition-colors">Create an account</button>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Main App View (for authenticated users) -->
        <div id="mainApp" class="hidden">
            <!-- Main Content Grid -->
            <main class="container mx-auto px-4 pt-24 pb-10">
                <!-- Grid updated for better responsiveness -->
                <div class="grid grid-cols-1 md:grid-cols-10 lg:grid-cols-12 gap-8">

                <!-- Left Column: Now visible on medium screens and up and is sticky -->
                <aside class="hidden md:block md:col-span-3 lg:col-span-3 space-y-6 self-start sticky top-24">
                    <!-- Profile Card -->
                    <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <div class="flex flex-col items-center text-center">
                            <img src="https://placehold.co/80x80/E2E8F0/475569?text=G" alt="Guest User" class="w-20 h-20 rounded-full mb-3 ring-4 ring-indigo-100">
                            <h2 class="text-xl font-bold text-slate-900">Guest User</h2>
                            <p class="text-sm text-slate-500 mt-1">Welcome to Naroop! Sign in to start sharing your positive stories.</p>
                        </div>
                        <div class="grid grid-cols-3 gap-2 text-center my-5">
                            <div>
                                <p class="font-bold text-lg">0</p>
                                <p class="text-xs text-slate-500">Stories</p>
                            </div>
                            <div>
                                <p class="font-bold text-lg">0</p>
                                <p class="text-xs text-slate-500">Followers</p>
                            </div>
                            <div>
                                <p class="font-bold text-lg">0</p>
                                <p class="text-xs text-slate-500">Following</p>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2 justify-center">
                            <span class="bg-slate-100 text-slate-500 text-xs font-semibold px-2.5 py-1 rounded-full">New User</span>
                        </div>
                    </div>
                    <!-- Navigation Links -->
                    <div class="bg-white p-3 rounded-xl border border-slate-200 shadow-sm">
                        <nav class="space-y-1">
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg bg-indigo-50 text-indigo-700 font-semibold">
                                <i data-lucide="layout-grid"></i> Feed
                            </a>
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="compass"></i> Explore
                            </a>
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="users"></i> Connect
                            </a>
                             <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="bookmark"></i> Bookmarks
                            </a>
                        </nav>
                    </div>
                </aside>

                <!-- Center Column: Spans adjusted for new breakpoints -->
                <div class="md:col-span-7 lg:col-span-6 space-y-6 pull-to-refresh" id="mainFeed">
                    <!-- Pull to Refresh Indicator -->
                    <div class="pull-refresh-indicator md:hidden" id="pullRefreshIndicator">
                        <i data-lucide="refresh-cw" class="w-5 h-5 text-indigo-600"></i>
                    </div>
                    <!-- Create Post -->
                    <div class="bg-white p-4 rounded-xl border border-slate-200 shadow-sm">
                        <div class="flex items-start gap-4">
                            <img src="https://placehold.co/40x40/E2E8F0/475569?text=JD" alt="User Avatar" class="w-10 h-10 rounded-full">
                            <div class="w-full space-y-3">
                                <input type="text" id="storyTitleInput" placeholder="Your story's title..." class="w-full border-slate-300 rounded-lg text-lg font-semibold placeholder-slate-400 focus:ring-indigo-500 focus:border-indigo-500">
                                <textarea id="storyContentInput" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2 placeholder-slate-400" rows="3" placeholder="What's your story today, John?"></textarea>

                                <!-- Category Selection -->
                                <div class="flex items-center gap-3">
                                    <label class="text-sm font-medium text-slate-700">Category:</label>
                                    <select id="storyCategorySelect" class="border-slate-300 rounded-lg text-sm focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="Personal Experience">Personal Experience</option>
                                        <option value="Inspiration">Inspiration</option>
                                        <option value="Technology">Technology</option>
                                        <option value="Travel">Travel</option>
                                        <option value="Career">Career</option>
                                        <option value="Lifestyle">Lifestyle</option>
                                        <option value="Education">Education</option>
                                        <option value="Health">Health</option>
                                        <option value="Creative">Creative</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>

                                <div id="titleSuggestionsContainer" class="hidden space-y-2"></div>
                                <!-- Image Preview -->
                                <div id="imagePreview" class="hidden">
                                    <img id="previewImg" class="w-full h-48 object-cover rounded-lg border border-slate-200" alt="Preview">
                                    <button id="removeImageBtn" class="mt-2 text-sm text-red-600 hover:text-red-800">Remove image</button>
                                </div>

                                <div class="flex justify-between items-center pt-3 border-t border-slate-200">
                                    <div class="flex items-center gap-4">
                                        <input type="file" id="imageUpload" accept="image/*" class="hidden">
                                        <button id="imageUploadBtn" class="text-slate-500 hover:text-indigo-600 transition-colors" title="Add image">
                                            <i data-lucide="image"></i>
                                        </button>
                                        <button class="text-slate-500 hover:text-indigo-600 transition-colors" title="Add video (coming soon)">
                                            <i data-lucide="video"></i>
                                        </button>
                                        <button id="suggestTitlesBtn" class="hidden items-center gap-1.5 text-sm font-semibold text-indigo-600 hover:text-indigo-800">✨ Suggest Titles</button>
                                    </div>
                                    <button id="postBtn" class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Post</button>
                                </div>
                            </div>
                        </div>
                     </div>
                    <!-- Posts will be loaded here dynamically -->
                    <div id="postsContainer" class="space-y-6"></div>

                </div>

                <!-- Right Column: Visible on large screens, span adjusted and is sticky -->
                <aside class="hidden lg:block lg:col-span-3 space-y-6 self-start sticky top-24">
                    <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <h3 class="font-bold text-lg mb-4">People You May Know</h3>
                        <div id="suggestedUsers" class="space-y-4">
                            <!-- Suggested users will be loaded dynamically -->
                        </div>
                    </div>
                     <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <h3 class="font-bold text-lg mb-4">Recent Activity</h3>
                        <div class="text-center py-8">
                            <i data-lucide="activity" class="w-12 h-12 text-slate-300 mx-auto mb-3"></i>
                            <p class="text-slate-500 text-sm">No recent activity yet</p>
                            <p class="text-slate-400 text-xs mt-1">Activity will appear here as you interact with stories</p>
                        </div>
                    </div>
                </aside>

                </div>
            </main>
        </div>
    </div>

    <!-- Comment Modal -->
    <div id="commentModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-30 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col">
            <div class="p-5 border-b border-slate-200 flex justify-between items-center">
                <h3 class="text-lg font-bold">Comments</h3>
                <button id="closeModalBtn" class="p-1 rounded-full hover:bg-slate-100">
                    <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                </button>
            </div>

            <!-- Story Info -->
            <div class="p-5 border-b border-slate-200 bg-slate-50">
                <p class="text-sm text-slate-600">Commenting on: <span id="modalStoryTitle" class="font-semibold"></span></p>
            </div>

            <!-- Existing Comments -->
            <div class="flex-1 overflow-y-auto p-5">
                <div id="existingComments" class="space-y-4 mb-6">
                    <!-- Comments will be loaded here -->
                </div>
            </div>

            <!-- Add Comment Form -->
            <div class="p-5 border-t border-slate-200 space-y-4">
                <textarea id="commentTextarea" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2 placeholder-slate-400" rows="3" placeholder="Share your thoughts..."></textarea>
                <div id="commentError" class="text-sm text-red-500"></div>
                <div class="flex justify-between items-center">
                    <button id="generateCommentBtn" class="flex items-center gap-1.5 text-sm font-semibold text-indigo-600 hover:text-indigo-800">✨ Generate Comment</button>
                    <button id="postCommentBtn" class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Post Comment</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Authentication Modal (Hidden by default) -->
    <div id="authModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-30 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-5 border-b border-slate-200 flex justify-between items-center">
                <h3 id="authModalTitle" class="text-lg font-bold">Welcome to Narrate</h3>
                <button id="closeAuthModalBtn" class="p-1 rounded-full hover:bg-slate-100">
                    <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                </button>
            </div>

            <!-- Login Form -->
            <div id="loginForm" class="p-5 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Email</label>
                    <input type="email" id="loginEmail" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="<EMAIL>">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Password</label>
                    <input type="password" id="loginPassword" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="••••••••">
                </div>
                <div id="loginError" class="text-sm text-red-500"></div>
                <div class="flex flex-col gap-3">
                    <button id="loginBtn" class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Sign In</button>
                    <button id="showSignupBtn" class="text-indigo-600 font-medium hover:text-indigo-800">Don't have an account? Sign up</button>
                </div>
            </div>

            <!-- Signup Form (Hidden initially) -->
            <div id="signupForm" class="hidden p-5 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Username</label>
                    <input type="text" id="signupUsername" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="johndoe">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Email</label>
                    <input type="email" id="signupEmail" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="<EMAIL>">
                </div>
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Password</label>
                    <input type="password" id="signupPassword" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2" placeholder="••••••••">
                    <div id="modalPasswordStrength"></div>
                </div>
                <div id="signupError" class="text-sm text-red-500"></div>
                <div class="flex flex-col gap-3">
                    <button id="signupBtn" class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Create Account</button>
                    <button id="showLoginBtn" class="text-indigo-600 font-medium hover:text-indigo-800">Already have an account? Sign in</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Search Modal -->
    <div id="mobileSearchModal" class="hidden fixed inset-0 bg-white z-40 md:hidden">
        <div class="p-4 border-b border-slate-200">
            <div class="flex items-center gap-3">
                <button id="closeMobileSearchBtn" class="p-2 rounded-full hover:bg-slate-100">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-600"></i>
                </button>
                <div class="flex-1 relative">
                    <i data-lucide="search" class="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 w-5 h-5"></i>
                    <input type="text" id="mobileSearchInput" placeholder="Search stories, people, or topics..." class="w-full bg-slate-100 border border-transparent rounded-full py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                </div>
            </div>
        </div>
        <div id="mobileSearchResults" class="flex-1 overflow-y-auto p-4">
            <!-- Mobile search results will appear here -->
        </div>
    </div>

    <!-- Messages Modal -->
    <div id="messagesModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-4xl h-[80vh] flex">
            <!-- Conversations List -->
            <div class="w-1/3 border-r border-slate-200 flex flex-col">
                <div class="p-4 border-b border-slate-200 flex justify-between items-center">
                    <h3 class="text-lg font-bold">Messages</h3>
                    <button id="closeMessagesBtn" class="p-1 rounded-full hover:bg-slate-100">
                        <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                    </button>
                </div>
                <div class="flex-1 overflow-y-auto">
                    <div id="conversationsList" class="divide-y divide-slate-100">
                        <!-- Conversations will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Chat Area -->
            <div class="flex-1 flex flex-col">
                <div id="chatHeader" class="hidden p-4 border-b border-slate-200 flex items-center gap-3">
                    <img id="chatPartnerAvatar" class="w-10 h-10 rounded-full" alt="">
                    <div>
                        <h4 id="chatPartnerName" class="font-semibold"></h4>
                        <p id="chatPartnerBio" class="text-sm text-slate-500"></p>
                    </div>
                </div>

                <div id="chatMessages" class="flex-1 overflow-y-auto p-4 space-y-3">
                    <div class="flex items-center justify-center h-full text-slate-500">
                        <div class="text-center">
                            <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-3 text-slate-300"></i>
                            <p>Select a conversation to start messaging</p>
                        </div>
                    </div>
                </div>

                <div id="chatInput" class="hidden p-4 border-t border-slate-200">
                    <div class="flex gap-3">
                        <input type="text" id="messageInput" placeholder="Type a message..." class="flex-1 border-slate-300 rounded-full px-4 py-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <button id="sendMessageBtn" class="bg-indigo-600 text-white p-2 rounded-full hover:bg-indigo-700 transition-colors">
                            <i data-lucide="send" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();

        document.addEventListener('DOMContentLoaded', () => {
            // --- Posts Data ---
            // Posts are now loaded from API

            // --- User Session Management ---
            let currentUser = null;

            // --- View Management ---
            function showLandingPage() {
                document.getElementById('landingPage').classList.remove('hidden');
                document.getElementById('mainApp').classList.add('hidden');
                document.querySelector('header').classList.add('hidden');

                // Hide mobile navigation on landing page
                updateMobileNavVisibility();

                // Update page title for landing page
                document.title = 'Naroop - Narrative of Our People | Share Your Stories, Connect with Community';

                // Clear any sensitive data from memory when showing landing page
                if (!currentUser) {
                    // Show welcome info message for first-time visitors
                    if (!localStorage.getItem('hasVisited')) {
                        setTimeout(() => {
                            showInfoMessage('Welcome to Naroop! Join our community where Black people share positive stories and experiences.', 6000);
                            localStorage.setItem('hasVisited', 'true');
                        }, 1000);
                    }
                }
            }

            function showMainApp() {
                document.getElementById('landingPage').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
                document.querySelector('header').classList.remove('hidden');

                // Show mobile navigation on main app
                updateMobileNavVisibility();

                // Update page title for authenticated users
                document.title = `Naroop - Welcome ${currentUser ? currentUser.username : 'User'}`;

                // Ensure all authenticated features are properly initialized
                initializeAuthenticatedFeatures();
            }

            // Initialize features that are only available to authenticated users
            function initializeAuthenticatedFeatures() {
                if (!currentUser) return;

                // Load user-specific data
                loadPosts();
                loadSuggestedUsers();

                // Initialize real-time features for authenticated users
                if (currentUser && currentUser.id) {
                    // Start periodic notification checks
                    startNotificationPolling();

                    // Initialize user activity tracking
                    trackUserActivity();
                }
            }

            // Start polling for notifications (only for authenticated users)
            function startNotificationPolling() {
                if (!currentUser || !currentUser.id) return;

                // Check notifications every 30 seconds
                const notificationInterval = setInterval(() => {
                    if (!currentUser) {
                        clearInterval(notificationInterval);
                        return;
                    }

                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown && notificationDropdown.classList.contains('hidden')) {
                        // Only update badge when dropdown is closed to avoid disrupting user
                        fetch(`/api/notifications/${currentUser.id}`)
                            .then(response => response.json())
                            .then(notifications => {
                                const unreadNotifications = notifications.filter(n => !n.read);
                                updateNotificationBadge(unreadNotifications.length);
                            })
                            .catch(error => console.error('Error checking notifications:', error));
                    }
                }, 30000);
            }

            // Initialize view based on authentication status
            function initializeView() {
                if (currentUser) {
                    showMainApp();
                    updateUIForUser();
                } else {
                    showLandingPage();
                }
            }

            // Update notification badge
            function updateNotificationBadge(count) {
                const badge = document.getElementById('notificationBadge');
                if (badge) {
                    if (count > 0) {
                        badge.textContent = count > 99 ? '99+' : count.toString();
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                }

                // Also update mobile notification badge
                updateMobileNotificationBadge(count);
            }

            // Update UI based on login status
            function updateUIForUser() {
                const userAvatar = document.getElementById('userAvatar');
                const profileCard = document.querySelector('aside .bg-white');

                if (currentUser) {
                    userAvatar.src = `https://placehold.co/40x40/E2E8F0/475569?text=${currentUser.username.substring(0, 2).toUpperCase()}`;
                    userAvatar.alt = currentUser.username;
                    if (profileCard) {
                        profileCard.querySelector('h2').textContent = currentUser.username;
                        // Update profile stats if available
                        const statsElements = profileCard.querySelectorAll('.font-bold.text-lg');
                        if (statsElements.length >= 3) {
                            statsElements[0].textContent = currentUser.stories || 0;
                            statsElements[1].textContent = currentUser.followers || 0;
                            statsElements[2].textContent = currentUser.following || 0;
                        }
                    }

                    // Show authenticated-only UI elements
                    showAuthenticatedElements();
                } else {
                     userAvatar.src = `https://placehold.co/40x40/E2E8F0/475569?text=G`;
                     userAvatar.alt = "Guest";
                     if (profileCard) {
                        profileCard.querySelector('h2').textContent = "Guest User";
                    }

                    // Hide authenticated-only UI elements
                    hideAuthenticatedElements();
                }
            }

            // Show UI elements that should only be visible to authenticated users
            function showAuthenticatedElements() {
                // Show notification and message buttons
                const notificationBtn = document.getElementById('notificationBtn');
                const messagesBtn = document.getElementById('messagesBtn');

                if (notificationBtn) notificationBtn.style.display = '';
                if (messagesBtn) messagesBtn.style.display = '';

                // Show write button
                const writeButtons = document.querySelectorAll('button:has(i[data-lucide="pen-square"])');
                writeButtons.forEach(btn => btn.style.display = '');
            }

            // Hide UI elements that should only be visible to authenticated users
            function hideAuthenticatedElements() {
                // Hide notification and message buttons
                const notificationBtn = document.getElementById('notificationBtn');
                const messagesBtn = document.getElementById('messagesBtn');

                if (notificationBtn) notificationBtn.style.display = 'none';
                if (messagesBtn) messagesBtn.style.display = 'none';

                // Hide write button
                const writeButtons = document.querySelectorAll('button:has(i[data-lucide="pen-square"])');
                writeButtons.forEach(btn => btn.style.display = 'none');

                // Hide notification badge
                const notificationBadge = document.getElementById('notificationBadge');
                if (notificationBadge) notificationBadge.classList.add('hidden');
            }

            // --- Success Message Function ---
            function showSuccessMessage(message) {
                // Create success notification
                const notification = document.createElement('div');
                notification.className = 'fixed top-20 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
                notification.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i data-lucide="check-circle" class="w-5 h-5"></i>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                // Trigger animation
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Auto-remove after 3 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);

                // Re-initialize icons for the notification
                lucide.createIcons();
            }

            // --- Form Validation Functions ---
            function validateEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            function validateUsername(username) {
                const usernameRegex = /^[a-zA-Z0-9_]+$/;
                return usernameRegex.test(username) && username.length >= 3 && username.length <= 20;
            }

            function validatePassword(password) {
                const errors = [];

                if (password.length < 6) {
                    errors.push('Password must be at least 6 characters');
                }

                if (password.length > 50) {
                    errors.push('Password must be less than 50 characters');
                }

                if (!/[a-z]/.test(password)) {
                    errors.push('Password must contain at least one lowercase letter');
                }

                if (!/[A-Z]/.test(password)) {
                    errors.push('Password must contain at least one uppercase letter');
                }

                if (!/[0-9]/.test(password)) {
                    errors.push('Password must contain at least one number');
                }

                return {
                    isValid: errors.length === 0,
                    errors: errors,
                    strength: getPasswordStrength(password)
                };
            }

            // --- Security Utilities ---

            // Content Security Policy helpers
            const SecurityUtils = {
                // Sanitize HTML to prevent XSS
                sanitizeHTML(str) {
                    const temp = document.createElement('div');
                    temp.textContent = str;
                    return temp.innerHTML;
                },

                // Validate and sanitize URLs
                sanitizeURL(url) {
                    try {
                        const urlObj = new URL(url);
                        // Only allow http and https protocols
                        if (!['http:', 'https:'].includes(urlObj.protocol)) {
                            return null;
                        }
                        return urlObj.href;
                    } catch {
                        return null;
                    }
                },

                // Rate limiting for API calls
                rateLimiter: new Map(),

                checkRateLimit(action, limit = 10, windowMs = 60000) {
                    const now = Date.now();
                    const key = `${action}_${currentUser?.id || 'anonymous'}`;

                    if (!this.rateLimiter.has(key)) {
                        this.rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
                        return true;
                    }

                    const data = this.rateLimiter.get(key);

                    if (now > data.resetTime) {
                        // Reset the counter
                        this.rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
                        return true;
                    }

                    if (data.count >= limit) {
                        return false;
                    }

                    data.count++;
                    return true;
                },

                // Input validation with security focus
                validateInput(input, type, options = {}) {
                    const { maxLength = 1000, minLength = 0, allowHTML = false } = options;

                    if (typeof input !== 'string') {
                        return { isValid: false, error: 'Input must be a string' };
                    }

                    if (input.length < minLength) {
                        return { isValid: false, error: `Input must be at least ${minLength} characters` };
                    }

                    if (input.length > maxLength) {
                        return { isValid: false, error: `Input must be less than ${maxLength} characters` };
                    }

                    // Check for potential XSS patterns
                    if (!allowHTML) {
                        const xssPatterns = [
                            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
                            /javascript:/gi,
                            /on\w+\s*=/gi,
                            /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi
                        ];

                        for (const pattern of xssPatterns) {
                            if (pattern.test(input)) {
                                return { isValid: false, error: 'Input contains potentially dangerous content' };
                            }
                        }
                    }

                    return { isValid: true, sanitized: allowHTML ? input : this.sanitizeHTML(input) };
                },

                // Generate secure random tokens
                generateSecureToken(length = 32) {
                    const array = new Uint8Array(length);
                    crypto.getRandomValues(array);
                    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
                }
            };

            function validatePostContent(title, content) {
                const errors = [];

                // Use security utilities for validation
                const titleValidation = SecurityUtils.validateInput(title, 'text', {
                    minLength: 5,
                    maxLength: 100,
                    allowHTML: false
                });

                const contentValidation = SecurityUtils.validateInput(content, 'text', {
                    minLength: 10,
                    maxLength: 5000,
                    allowHTML: false
                });

                if (!titleValidation.isValid) {
                    errors.push(titleValidation.error);
                }

                if (!contentValidation.isValid) {
                    errors.push(contentValidation.error);
                }

                // Additional content moderation
                const inappropriateWords = ['spam', 'scam', 'fake', 'hate', 'abuse'];
                const combinedText = (title + ' ' + content).toLowerCase();
                const foundInappropriate = inappropriateWords.find(word => combinedText.includes(word));
                if (foundInappropriate) {
                    errors.push('Content contains inappropriate language');
                }

                return {
                    isValid: errors.length === 0,
                    errors: errors
                };
            }

            function getPasswordStrength(password) {
                let score = 0;

                // Length bonus
                if (password.length >= 8) score += 1;
                if (password.length >= 12) score += 1;

                // Character variety bonus
                if (/[a-z]/.test(password)) score += 1;
                if (/[A-Z]/.test(password)) score += 1;
                if (/[0-9]/.test(password)) score += 1;
                if (/[^a-zA-Z0-9]/.test(password)) score += 1;

                if (score <= 2) return 'weak';
                if (score <= 4) return 'medium';
                return 'strong';
            }

            function showPasswordStrength(password, containerId) {
                const container = document.getElementById(containerId);
                if (!container) return;

                const strength = getPasswordStrength(password);
                const colors = {
                    weak: 'bg-red-500',
                    medium: 'bg-yellow-500',
                    strong: 'bg-green-500'
                };

                container.innerHTML = `
                    <div class="mt-2">
                        <div class="flex items-center gap-2 text-xs">
                            <span class="text-slate-600">Password strength:</span>
                            <span class="capitalize font-medium ${strength === 'weak' ? 'text-red-600' : strength === 'medium' ? 'text-yellow-600' : 'text-green-600'}">${strength}</span>
                        </div>
                        <div class="mt-1 h-1 bg-slate-200 rounded-full overflow-hidden">
                            <div class="h-full ${colors[strength]} transition-all duration-300" style="width: ${strength === 'weak' ? '33%' : strength === 'medium' ? '66%' : '100%'}"></div>
                        </div>
                    </div>
                `;
            }

            // --- Real-time Validation Event Listeners ---
            // Landing page password strength
            document.getElementById('landingSignupPassword').addEventListener('input', (e) => {
                showPasswordStrength(e.target.value, 'landingPasswordStrength');
            });

            // Modal password strength
            document.getElementById('signupPassword').addEventListener('input', (e) => {
                showPasswordStrength(e.target.value, 'modalPasswordStrength');
            });

            // Real-time email validation
            function addEmailValidation(inputId, errorId) {
                const input = document.getElementById(inputId);
                const errorDiv = document.getElementById(errorId);

                input.addEventListener('blur', () => {
                    const email = input.value.trim();
                    if (email && !validateEmail(email)) {
                        input.classList.add('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
                        input.classList.remove('border-slate-300', 'focus:border-indigo-500', 'focus:ring-indigo-500');
                    } else {
                        input.classList.remove('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
                        input.classList.add('border-slate-300', 'focus:border-indigo-500', 'focus:ring-indigo-500');
                    }
                });
            }

            // Real-time username validation
            function addUsernameValidation(inputId) {
                const input = document.getElementById(inputId);

                input.addEventListener('blur', () => {
                    const username = input.value.trim();
                    if (username && !validateUsername(username)) {
                        input.classList.add('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
                        input.classList.remove('border-slate-300', 'focus:border-indigo-500', 'focus:ring-indigo-500');
                    } else {
                        input.classList.remove('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
                        input.classList.add('border-slate-300', 'focus:border-indigo-500', 'focus:ring-indigo-500');
                    }
                });
            }

            // Apply real-time validation
            addEmailValidation('landingSignupEmail', 'landingSignupError');
            addEmailValidation('landingLoginEmail', 'landingLoginError');
            addEmailValidation('loginEmail', 'loginError');
            addEmailValidation('signupEmail', 'signupError');
            addUsernameValidation('landingSignupUsername');
            addUsernameValidation('signupUsername');

            // --- Session Management ---
            const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

            function saveUserSession(user) {
                const sessionData = {
                    user: user,
                    timestamp: Date.now(),
                    expiresAt: Date.now() + SESSION_DURATION,
                    sessionId: SecurityUtils.generateSecureToken(),
                    userAgent: navigator.userAgent,
                    ipFingerprint: generateIPFingerprint()
                };
                localStorage.setItem('currentUser', JSON.stringify(sessionData));
                currentUser = user;
            }

            // Generate a simple fingerprint for additional security
            function generateIPFingerprint() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Security fingerprint', 2, 2);
                return canvas.toDataURL().slice(-50); // Use last 50 chars as fingerprint
            }

            function loadUserSession() {
                try {
                    const sessionData = JSON.parse(localStorage.getItem('currentUser'));
                    if (!sessionData) return null;

                    // Check if session has expired
                    if (Date.now() > sessionData.expiresAt) {
                        clearUserSession();
                        showSessionExpiredMessage();
                        return null;
                    }

                    // Validate session integrity
                    if (sessionData.userAgent && sessionData.userAgent !== navigator.userAgent) {
                        console.warn('Session user agent mismatch detected');
                        clearUserSession();
                        showErrorMessage('Session security validation failed. Please sign in again.');
                        return null;
                    }

                    if (sessionData.ipFingerprint && sessionData.ipFingerprint !== generateIPFingerprint()) {
                        console.warn('Session fingerprint mismatch detected');
                        clearUserSession();
                        showErrorMessage('Session security validation failed. Please sign in again.');
                        return null;
                    }

                    // Extend session if user is active
                    if (sessionData.user) {
                        saveUserSession(sessionData.user);
                        return sessionData.user;
                    }
                } catch (error) {
                    console.error('Error loading session:', error);
                    clearUserSession();
                }
                return null;
            }

            function clearUserSession() {
                localStorage.removeItem('currentUser');
                currentUser = null;
            }

            function showSessionExpiredMessage() {
                const notification = document.createElement('div');
                notification.className = 'fixed top-20 right-4 bg-orange-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
                notification.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i data-lucide="clock" class="w-5 h-5"></i>
                        <span>Your session has expired. Please sign in again.</span>
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 5000);

                lucide.createIcons();
            }

            // Initialize Firebase and session management
            async function initializeSession() {
                try {
                    // Initialize Firebase
                    const firebaseInitialized = await window.FirebaseAuth.init();

                    if (!firebaseInitialized) {
                        console.error('Firebase initialization failed');
                        // Fall back to local session management
                        currentUser = loadUserSession();
                        return;
                    }

                    // Listen for Firebase auth state changes
                    window.FirebaseAuth.onAuthStateChanged(async (user) => {
                        if (user) {
                            // User is signed in, sync with our backend
                            try {
                                const response = await safeFetch('/api/login', {
                                    method: 'POST',
                                    body: JSON.stringify({
                                        uid: user.uid,
                                        email: user.email
                                    })
                                });

                                if (response.success) {
                                    currentUser = response.user;
                                    saveUserSession(currentUser);
                                    showMainApp();
                                    updateUIForUser();
                                } else {
                                    console.error('Backend login failed:', response.error);
                                    await window.FirebaseAuth.signOut();
                                }
                            } catch (error) {
                                console.error('Error syncing with backend:', error);
                                await window.FirebaseAuth.signOut();
                            }
                        } else {
                            // User is signed out
                            currentUser = null;
                            clearUserSession();
                            showLandingPage();
                        }
                    });

                } catch (error) {
                    console.error('Error initializing Firebase session:', error);
                    // Fall back to local session management
                    currentUser = loadUserSession();
                }
            }

            // Activity tracking to extend session
            let lastActivity = Date.now();

            function trackUserActivity() {
                lastActivity = Date.now();
                if (currentUser) {
                    // Extend session on activity
                    saveUserSession(currentUser);
                }
            }

            // Add activity listeners
            ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(event => {
                document.addEventListener(event, trackUserActivity, { passive: true });
            });

            // Initialize session management
            initializeSession();

            // --- Route Protection System ---

            // Define protected actions that require authentication
            const protectedActions = [
                'createPost',
                'likePost',
                'commentPost',
                'sharePost',
                'followUser',
                'sendMessage',
                'viewNotifications',
                'viewProfile',
                'editProfile'
            ];

            // Route protection middleware
            function requireAuth(action, callback) {
                if (!currentUser) {
                    // Show authentication required message
                    showAuthRequiredMessage(action);
                    // Redirect to landing page if not already there
                    if (document.getElementById('landingPage').classList.contains('hidden')) {
                        showLandingPage();
                    }
                    return false;
                }

                // User is authenticated, execute the callback
                if (typeof callback === 'function') {
                    callback();
                }
                return true;
            }

            // Show authentication required message
            function showAuthRequiredMessage(action) {
                const actionMessages = {
                    createPost: 'Please sign in to create a post',
                    likePost: 'Please sign in to like posts',
                    commentPost: 'Please sign in to comment',
                    sharePost: 'Please sign in to share posts',
                    followUser: 'Please sign in to follow users',
                    sendMessage: 'Please sign in to send messages',
                    viewNotifications: 'Please sign in to view notifications',
                    viewProfile: 'Please sign in to view profiles',
                    editProfile: 'Please sign in to edit your profile'
                };

                const message = actionMessages[action] || 'Please sign in to continue';

                const notification = document.createElement('div');
                notification.className = 'fixed top-20 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
                notification.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i data-lucide="lock" class="w-5 h-5"></i>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 4000);

                lucide.createIcons();
            }

            // Page visibility and focus handlers for route protection
            function handlePageVisibilityChange() {
                if (!document.hidden && document.visibilityState === 'visible') {
                    // Page became visible, check authentication
                    const sessionUser = loadUserSession();
                    if (!sessionUser && document.getElementById('landingPage').classList.contains('hidden')) {
                        // User session expired while page was hidden, redirect to landing
                        showLandingPage();
                    }
                }
            }

            function handleWindowFocus() {
                // Window gained focus, validate session
                const sessionUser = loadUserSession();
                if (!sessionUser && currentUser) {
                    // Session expired, redirect to landing page
                    currentUser = null;
                    showLandingPage();
                    showSessionExpiredMessage();
                }
            }

            // Browser navigation protection
            function handlePopState(event) {
                // Handle browser back/forward buttons
                const sessionUser = loadUserSession();
                if (!sessionUser) {
                    // No valid session, ensure landing page is shown
                    showLandingPage();
                    // Update browser history to prevent navigation loops
                    history.replaceState(null, '', window.location.pathname);
                }
            }

            // Protect against direct URL manipulation
            function protectDirectAccess() {
                // Check if user is trying to access protected content directly
                const sessionUser = loadUserSession();
                if (!sessionUser) {
                    // No valid session, show landing page
                    showLandingPage();
                    // Clear any URL fragments or parameters that might indicate protected content
                    if (window.location.hash || window.location.search) {
                        history.replaceState(null, '', window.location.pathname);
                    }
                }
            }

            // Initialize route protection
            function initializeRouteProtection() {
                // Add event listeners for route protection
                document.addEventListener('visibilitychange', handlePageVisibilityChange);
                window.addEventListener('focus', handleWindowFocus);
                window.addEventListener('popstate', handlePopState);

                // Protect against direct access on page load
                protectDirectAccess();

                // Override browser refresh behavior
                window.addEventListener('beforeunload', (event) => {
                    // Save current authentication state for post-refresh validation
                    if (currentUser) {
                        sessionStorage.setItem('authCheck', 'true');
                    } else {
                        sessionStorage.removeItem('authCheck');
                    }
                });

                // Check authentication after page refresh
                const authCheck = sessionStorage.getItem('authCheck');
                if (authCheck && !currentUser) {
                    // User was authenticated before refresh but session is invalid now
                    sessionStorage.removeItem('authCheck');
                    showLandingPage();
                }
            }



            // --- Protected Post Interaction Handlers ---
            window.handleLikeClick = function(button) {
                requireAuth('likePost', () => {
                    // Like functionality would go here
                    console.log('Like button clicked');
                    // TODO: Implement actual like functionality with API call
                });
            };

            window.handleCommentClick = function(button) {
                requireAuth('commentPost', () => {
                    // Get the article element and trigger comment modal
                    const article = button.closest('article');
                    if (article) {
                        const title = article.dataset.storyTitle;
                        const content = article.dataset.storyContent;
                        const modal = document.getElementById('commentModal');
                        const modalStoryTitle = document.getElementById('modalStoryTitle');
                        const commentTextarea = document.getElementById('commentTextarea');
                        const commentError = document.getElementById('commentError');

                        modalStoryTitle.textContent = title;
                        modal.dataset.storyTitle = title;
                        modal.dataset.storyContent = content;
                        commentTextarea.value = '';
                        commentError.textContent = '';
                        modal.classList.remove('hidden');
                    }
                });
            };

            window.handleShareClick = function(button) {
                requireAuth('sharePost', () => {
                    // Share functionality would go here
                    console.log('Share button clicked');
                    // TODO: Implement actual share functionality with API call
                });
            };

            window.handleBookmarkClick = function(button) {
                requireAuth('viewProfile', () => {
                    // Bookmark functionality would go here
                    console.log('Bookmark button clicked');
                    // TODO: Implement actual bookmark functionality
                });
            };

            // --- Loading State Management ---

            // Show loading state on button
            function setButtonLoading(button, isLoading, originalText = null) {
                if (isLoading) {
                    if (!button.dataset.originalText) {
                        button.dataset.originalText = button.textContent;
                    }
                    button.disabled = true;
                    button.classList.add('btn-loading');
                    button.textContent = originalText || button.dataset.originalText;
                } else {
                    button.disabled = false;
                    button.classList.remove('btn-loading');
                    button.textContent = button.dataset.originalText || originalText || 'Submit';
                }
            }

            // Show loading overlay on element
            function showLoadingOverlay(element, message = 'Loading...') {
                const overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="text-center">
                        <div class="loading-spinner loading-spinner-large loading-spinner-primary mx-auto mb-4"></div>
                        <p class="text-slate-600 font-medium">${message}</p>
                    </div>
                `;

                element.style.position = 'relative';
                element.appendChild(overlay);
                return overlay;
            }

            // Hide loading overlay
            function hideLoadingOverlay(element) {
                const overlay = element.querySelector('.loading-overlay');
                if (overlay) {
                    overlay.remove();
                }
            }

            // Show skeleton loading for posts
            function showPostsSkeleton() {
                const postsContainer = document.getElementById('postsContainer');
                if (!postsContainer) return;

                const skeletonHTML = Array(3).fill().map(() => `
                    <article class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-6">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="w-10 h-10 bg-slate-200 rounded-full skeleton"></div>
                            <div class="flex-1">
                                <div class="h-4 bg-slate-200 rounded skeleton mb-2" style="width: 120px;"></div>
                                <div class="h-3 bg-slate-200 rounded skeleton" style="width: 80px;"></div>
                            </div>
                        </div>
                        <div class="h-6 bg-slate-200 rounded skeleton mb-3" style="width: 70%;"></div>
                        <div class="space-y-2 mb-4">
                            <div class="h-4 bg-slate-200 rounded skeleton"></div>
                            <div class="h-4 bg-slate-200 rounded skeleton"></div>
                            <div class="h-4 bg-slate-200 rounded skeleton" style="width: 80%;"></div>
                        </div>
                        <div class="flex justify-around pt-4 border-t border-slate-200">
                            <div class="h-8 bg-slate-200 rounded skeleton" style="width: 60px;"></div>
                            <div class="h-8 bg-slate-200 rounded skeleton" style="width: 60px;"></div>
                            <div class="h-8 bg-slate-200 rounded skeleton" style="width: 60px;"></div>
                            <div class="h-8 bg-slate-200 rounded skeleton" style="width: 60px;"></div>
                        </div>
                    </article>
                `).join('');

                postsContainer.innerHTML = skeletonHTML;
            }

            // Show skeleton loading for suggested users
            function showUsersSkeleton() {
                const suggestedUsersContainer = document.querySelector('#suggestedUsers .space-y-3');
                if (!suggestedUsersContainer) return;

                const skeletonHTML = Array(5).fill().map(() => `
                    <div class="flex items-center gap-3 p-3 rounded-lg">
                        <div class="w-10 h-10 bg-slate-200 rounded-full skeleton"></div>
                        <div class="flex-1">
                            <div class="h-4 bg-slate-200 rounded skeleton mb-1" style="width: 80px;"></div>
                            <div class="h-3 bg-slate-200 rounded skeleton" style="width: 60px;"></div>
                        </div>
                        <div class="h-8 bg-slate-200 rounded skeleton" style="width: 60px;"></div>
                    </div>
                `).join('');

                suggestedUsersContainer.innerHTML = skeletonHTML;
            }

            // Show loading state for form submission
            function setFormLoading(form, isLoading) {
                const inputs = form.querySelectorAll('input, textarea, select');
                const buttons = form.querySelectorAll('button');

                inputs.forEach(input => {
                    input.disabled = isLoading;
                    if (isLoading) {
                        input.classList.add('opacity-50');
                    } else {
                        input.classList.remove('opacity-50');
                    }
                });

                buttons.forEach(button => {
                    if (button.type === 'submit') {
                        setButtonLoading(button, isLoading);
                    } else {
                        button.disabled = isLoading;
                    }
                });
            }

            // --- Comprehensive Error Handling ---

            // Error types for better categorization
            const ErrorTypes = {
                NETWORK: 'network',
                VALIDATION: 'validation',
                AUTHENTICATION: 'authentication',
                SERVER: 'server',
                UNKNOWN: 'unknown'
            };

            // Centralized error handler
            function handleError(error, context = '', showToUser = true) {
                console.error(`Error in ${context}:`, error);

                let errorType = ErrorTypes.UNKNOWN;
                let userMessage = 'An unexpected error occurred. Please try again.';

                // Categorize error and provide appropriate message
                if (error instanceof TypeError && error.message.includes('fetch')) {
                    errorType = ErrorTypes.NETWORK;
                    userMessage = 'Network error. Please check your internet connection and try again.';
                } else if (error.name === 'ValidationError') {
                    errorType = ErrorTypes.VALIDATION;
                    userMessage = error.message;
                } else if (error.status === 401 || error.status === 403) {
                    errorType = ErrorTypes.AUTHENTICATION;
                    userMessage = 'Authentication failed. Please sign in again.';
                } else if (error.status >= 400 && error.status < 500) {
                    errorType = ErrorTypes.VALIDATION;
                    userMessage = error.message || 'Invalid request. Please check your input and try again.';
                } else if (error.status >= 500) {
                    errorType = ErrorTypes.SERVER;
                    userMessage = 'Server error. Please try again later.';
                }

                // Log error for debugging
                const errorLog = {
                    type: errorType,
                    context: context,
                    message: error.message,
                    stack: error.stack,
                    timestamp: new Date().toISOString()
                };

                // Store error logs in localStorage for debugging (limit to last 10)
                try {
                    const errorLogs = JSON.parse(localStorage.getItem('errorLogs') || '[]');
                    errorLogs.unshift(errorLog);
                    if (errorLogs.length > 10) errorLogs.pop();
                    localStorage.setItem('errorLogs', JSON.stringify(errorLogs));
                } catch (e) {
                    console.warn('Could not store error log:', e);
                }

                // Show error to user if requested
                if (showToUser) {
                    showErrorMessage(userMessage, errorType);
                }

                return { type: errorType, message: userMessage };
            }

            // Show error message to user
            function showErrorMessage(message, type = ErrorTypes.UNKNOWN) {
                const colors = {
                    [ErrorTypes.NETWORK]: 'bg-orange-500',
                    [ErrorTypes.VALIDATION]: 'bg-yellow-500',
                    [ErrorTypes.AUTHENTICATION]: 'bg-red-500',
                    [ErrorTypes.SERVER]: 'bg-red-600',
                    [ErrorTypes.UNKNOWN]: 'bg-gray-500'
                };

                const icons = {
                    [ErrorTypes.NETWORK]: 'wifi-off',
                    [ErrorTypes.VALIDATION]: 'alert-triangle',
                    [ErrorTypes.AUTHENTICATION]: 'lock',
                    [ErrorTypes.SERVER]: 'server',
                    [ErrorTypes.UNKNOWN]: 'alert-circle'
                };

                const notification = document.createElement('div');
                notification.className = `fixed top-20 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300 max-w-sm`;
                notification.innerHTML = `
                    <div class="flex items-start gap-3">
                        <i data-lucide="${icons[type]}" class="w-5 h-5 mt-0.5 flex-shrink-0"></i>
                        <div class="flex-1">
                            <p class="font-medium text-sm">${message}</p>
                            <button class="text-xs opacity-75 hover:opacity-100 mt-1" onclick="this.parentElement.parentElement.parentElement.remove()">
                                Dismiss
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Auto-remove after 6 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.classList.add('translate-x-full');
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 300);
                    }
                }, 6000);

                lucide.createIcons();
            }

            // Enhanced fetch wrapper with error handling, retry mechanism, and caching
            async function safeFetch(url, options = {}, retries = 2) {
                const { retryCount = 0, useCache = true, ...fetchOptions } = options;

                // Check cache for GET requests
                if ((!fetchOptions.method || fetchOptions.method === 'GET') && useCache) {
                    const cached = getCachedResponse(url);
                    if (cached) {
                        return cached;
                    }
                }

                try {
                    // Add CSRF token for non-GET requests
                    const headers = {
                        'Content-Type': 'application/json',
                        ...fetchOptions.headers
                    };

                    if (fetchOptions.method && fetchOptions.method !== 'GET') {
                        headers['X-CSRF-Token'] = csrfToken;
                    }

                    const response = await fetch(url, {
                        ...fetchOptions,
                        headers
                    });

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({}));
                        const error = new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                        error.status = response.status;

                        // Don't retry client errors (4xx), only server errors (5xx) and network issues
                        if (response.status >= 500 && retryCount < retries) {
                            console.warn(`Request failed with ${response.status}, retrying... (${retryCount + 1}/${retries})`);
                            await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000)); // Exponential backoff
                            return safeFetch(url, { ...options, retryCount: retryCount + 1 }, retries);
                        }

                        throw error;
                    }

                    const data = await response.json();

                    // Cache GET responses
                    if ((!fetchOptions.method || fetchOptions.method === 'GET') && useCache) {
                        setCachedResponse(url, data);
                    }

                    return data;
                } catch (error) {
                    if (error.name === 'TypeError' && error.message.includes('fetch')) {
                        // Network error - retry if we haven't exceeded retry count
                        if (retryCount < retries) {
                            console.warn(`Network error, retrying... (${retryCount + 1}/${retries})`);
                            await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000)); // Exponential backoff
                            return safeFetch(url, { ...options, retryCount: retryCount + 1 }, retries);
                        }

                        const networkError = new Error('Network connection failed after retries');
                        networkError.type = ErrorTypes.NETWORK;
                        throw networkError;
                    }
                    throw error;
                }
            }

            // Global error handler for unhandled errors
            window.addEventListener('error', (event) => {
                handleError(event.error, 'Global error handler');
            });

            // Connection status monitoring
            let isOnline = navigator.onLine;

            function updateConnectionStatus() {
                const wasOnline = isOnline;
                isOnline = navigator.onLine;

                if (!wasOnline && isOnline) {
                    showSuccessMessage('Connection restored! You\'re back online.');
                } else if (wasOnline && !isOnline) {
                    showErrorMessage('Connection lost. Some features may not work until you\'re back online.', ErrorTypes.NETWORK);
                }
            }

            window.addEventListener('online', updateConnectionStatus);
            window.addEventListener('offline', updateConnectionStatus);

            // Check connection status periodically
            setInterval(() => {
                if (!navigator.onLine && isOnline) {
                    updateConnectionStatus();
                }
            }, 5000);

            // CSRF Protection
            let csrfToken = SecurityUtils.generateSecureToken();

            // Regenerate CSRF token periodically
            setInterval(() => {
                csrfToken = SecurityUtils.generateSecureToken();
            }, 30 * 60 * 1000); // Every 30 minutes

            // --- Performance Optimizations ---

            // Lazy loading for images
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const src = img.dataset.src;
                        if (src) {
                            img.src = src;
                            img.removeAttribute('data-src');
                            img.classList.remove('lazy-load');
                            observer.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Function to make images lazy-loadable
            function makeLazyLoadable(img, src) {
                img.dataset.src = src;
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGMUY1RjkiLz48L3N2Zz4='; // 1x1 placeholder
                img.classList.add('lazy-load');
                imageObserver.observe(img);
            }

            // API response caching
            const apiCache = new Map();
            const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

            function getCachedResponse(url) {
                const cached = apiCache.get(url);
                if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
                    return cached.data;
                }
                return null;
            }

            function setCachedResponse(url, data) {
                apiCache.set(url, {
                    data: data,
                    timestamp: Date.now()
                });

                // Limit cache size to prevent memory issues
                if (apiCache.size > 50) {
                    const firstKey = apiCache.keys().next().value;
                    apiCache.delete(firstKey);
                }
            }

            // Debounce utility function
            function debounce(func, wait, immediate) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        timeout = null;
                        if (!immediate) func(...args);
                    };
                    const callNow = immediate && !timeout;
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                    if (callNow) func(...args);
                };
            }

            // Throttle utility function
            function throttle(func, limit) {
                let inThrottle;
                return function(...args) {
                    if (!inThrottle) {
                        func.apply(this, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                };
            }

            // DOM fragment optimization for batch operations
            function createElementsFromHTML(htmlString) {
                const template = document.createElement('template');
                template.innerHTML = htmlString.trim();
                return template.content;
            }

            // Intersection Observer for infinite scroll
            let isLoadingMorePosts = false;
            const loadMoreObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !isLoadingMorePosts) {
                        loadMorePosts();
                    }
                });
            }, {
                rootMargin: '100px'
            });

            // Load more posts function (for infinite scroll)
            async function loadMorePosts() {
                if (isLoadingMorePosts) return;

                isLoadingMorePosts = true;
                const postsContainer = document.getElementById('postsContainer');

                try {
                    // Add loading indicator
                    const loadingDiv = document.createElement('div');
                    loadingDiv.className = 'text-center py-4';
                    loadingDiv.innerHTML = '<div class="loading-spinner mx-auto"></div>';
                    postsContainer.appendChild(loadingDiv);

                    // Simulate loading more posts (in real app, this would be paginated API call)
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // Remove loading indicator
                    loadingDiv.remove();

                } catch (error) {
                    console.error('Error loading more posts:', error);
                } finally {
                    isLoadingMorePosts = false;
                }
            }

            // Performance monitoring
            const performanceMetrics = {
                startTime: performance.now(),
                loadTimes: [],

                recordLoadTime(operation, startTime) {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    this.loadTimes.push({ operation, duration, timestamp: Date.now() });

                    // Keep only last 50 metrics
                    if (this.loadTimes.length > 50) {
                        this.loadTimes.shift();
                    }

                    // Log slow operations
                    if (duration > 1000) {
                        console.warn(`Slow operation detected: ${operation} took ${duration.toFixed(2)}ms`);
                    }
                },

                getAverageLoadTime(operation) {
                    const operationTimes = this.loadTimes.filter(metric => metric.operation === operation);
                    if (operationTimes.length === 0) return 0;
                    const total = operationTimes.reduce((sum, metric) => sum + metric.duration, 0);
                    return total / operationTimes.length;
                }
            };

            // Global handler for unhandled promise rejections
            window.addEventListener('unhandledrejection', (event) => {
                handleError(event.reason, 'Unhandled promise rejection');
                event.preventDefault(); // Prevent console error
            });

            // --- Enhanced User Feedback Mechanisms ---

            // Show confirmation dialog
            function showConfirmDialog(message, onConfirm, onCancel = null) {
                const overlay = document.createElement('div');
                overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                overlay.innerHTML = `
                    <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm mx-4 transform scale-95 transition-transform duration-200">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-slate-900">Confirm Action</h3>
                        </div>
                        <p class="text-slate-600 mb-6">${message}</p>
                        <div class="flex gap-3 justify-end">
                            <button id="cancelBtn" class="px-4 py-2 text-slate-600 hover:text-slate-800 font-medium">
                                Cancel
                            </button>
                            <button id="confirmBtn" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 font-medium">
                                Confirm
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(overlay);

                // Animate in
                setTimeout(() => {
                    overlay.querySelector('div').classList.remove('scale-95');
                    overlay.querySelector('div').classList.add('scale-100');
                }, 10);

                // Event handlers
                const confirmBtn = overlay.querySelector('#confirmBtn');
                const cancelBtn = overlay.querySelector('#cancelBtn');

                const cleanup = () => {
                    overlay.querySelector('div').classList.add('scale-95');
                    setTimeout(() => {
                        if (overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                    }, 200);
                };

                confirmBtn.addEventListener('click', () => {
                    cleanup();
                    if (onConfirm) onConfirm();
                });

                cancelBtn.addEventListener('click', () => {
                    cleanup();
                    if (onCancel) onCancel();
                });

                // Close on overlay click
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        cleanup();
                        if (onCancel) onCancel();
                    }
                });

                lucide.createIcons();
            }

            // Show progress indicator
            function showProgressIndicator(message, steps = []) {
                const overlay = document.createElement('div');
                overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                overlay.innerHTML = `
                    <div class="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
                        <div class="text-center">
                            <div class="loading-spinner loading-spinner-large loading-spinner-primary mx-auto mb-4"></div>
                            <h3 class="text-lg font-semibold text-slate-900 mb-2">${message}</h3>
                            <div id="progressSteps" class="text-sm text-slate-600">
                                ${steps.map((step, index) => `
                                    <div class="flex items-center gap-2 mb-1 opacity-50" data-step="${index}">
                                        <div class="w-4 h-4 rounded-full border-2 border-slate-300"></div>
                                        <span>${step}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(overlay);

                return {
                    updateStep: (stepIndex) => {
                        const steps = overlay.querySelectorAll('[data-step]');
                        steps.forEach((step, index) => {
                            if (index <= stepIndex) {
                                step.classList.remove('opacity-50');
                                step.classList.add('opacity-100');
                                const circle = step.querySelector('div');
                                circle.classList.remove('border-slate-300');
                                circle.classList.add('bg-green-500', 'border-green-500');
                                circle.innerHTML = '<i data-lucide="check" class="w-2 h-2 text-white"></i>';
                            }
                        });
                        lucide.createIcons();
                    },
                    close: () => {
                        if (overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                    }
                };
            }

            // Show info message
            function showInfoMessage(message, duration = 4000) {
                const notification = document.createElement('div');
                notification.className = 'fixed top-20 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
                notification.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i data-lucide="info" class="w-5 h-5"></i>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, duration);

                lucide.createIcons();
            }

            // Enhanced success message with action button
            function showSuccessMessageWithAction(message, actionText, actionCallback) {
                const notification = document.createElement('div');
                notification.className = 'fixed top-20 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300 max-w-sm';
                notification.innerHTML = `
                    <div class="flex items-start gap-3">
                        <i data-lucide="check-circle" class="w-5 h-5 mt-0.5 flex-shrink-0"></i>
                        <div class="flex-1">
                            <p class="font-medium text-sm">${message}</p>
                            ${actionText ? `
                                <button class="text-xs bg-white bg-opacity-20 hover:bg-opacity-30 px-2 py-1 rounded mt-2 transition-colors" onclick="this.parentElement.parentElement.parentElement.remove(); (${actionCallback.toString()})()">
                                    ${actionText}
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 5000);

                lucide.createIcons();
            }

            // Initialize the application
            initializeSession();
            initializeRouteProtection();
            initializeView();

            // --- Authentication Modal Functions ---
            const authModal = document.getElementById('authModal');
            const closeAuthModalBtn = document.getElementById('closeAuthModalBtn');
            const loginForm = document.getElementById('loginForm');
            const signupForm = document.getElementById('signupForm');
            const showSignupBtn = document.getElementById('showSignupBtn');
            const showLoginBtn = document.getElementById('showLoginBtn');
            const authModalTitle = document.getElementById('authModalTitle');

            window.toggleAuthModal = function() {
                if (currentUser) {
                    showConfirmDialog(
                        'Are you sure you want to logout? You will need to sign in again to access your account.',
                        async () => {
                            try {
                                await window.FirebaseAuth.signOut();
                                showSuccessMessage('You have been logged out successfully.');
                            } catch (error) {
                                console.error('Logout error:', error);
                                // Fallback to local logout
                                clearUserSession();
                                showLandingPage();
                                showSuccessMessage('You have been logged out successfully.');
                            }
                        }
                    );
                } else {
                    // If we're on landing page, scroll to auth forms instead of showing modal
                    if (!document.getElementById('landingPage').classList.contains('hidden')) {
                        document.querySelector('#landingLoginForm').scrollIntoView({ behavior: 'smooth' });
                    } else {
                        authModal.classList.remove('hidden');
                        showLoginForm();
                    }
                }
            };
            function showLoginForm() {
                loginForm.classList.remove('hidden');
                signupForm.classList.add('hidden');
                authModalTitle.textContent = 'Welcome Back';
            }
            function showSignupForm() {
                loginForm.classList.add('hidden');
                signupForm.classList.remove('hidden');
                authModalTitle.textContent = 'Join Narrate';
            }
            function closeAuthModal() {
                authModal.classList.add('hidden');
            }
            closeAuthModalBtn.addEventListener('click', closeAuthModal);
            showSignupBtn.addEventListener('click', showSignupForm);
            showLoginBtn.addEventListener('click', showLoginForm);
            authModal.addEventListener('click', (e) => e.target === authModal && closeAuthModal());

            // --- Landing Page Authentication ---
            // Hero button handlers
            document.getElementById('heroSignupBtn').addEventListener('click', () => {
                document.querySelector('#landingSignupForm').scrollIntoView({ behavior: 'smooth' });
            });

            document.getElementById('heroLoginBtn').addEventListener('click', () => {
                document.querySelector('#landingLoginForm').scrollIntoView({ behavior: 'smooth' });
            });

            // Form switching
            document.getElementById('switchToLogin').addEventListener('click', () => {
                document.querySelector('#landingLoginForm').scrollIntoView({ behavior: 'smooth' });
            });

            document.getElementById('switchToSignup').addEventListener('click', () => {
                document.querySelector('#landingSignupForm').scrollIntoView({ behavior: 'smooth' });
            });

            // Landing page signup form
            document.getElementById('landingSignupForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const username = document.getElementById('landingSignupUsername').value.trim();
                const email = document.getElementById('landingSignupEmail').value.trim();
                const password = document.getElementById('landingSignupPassword').value;
                const errorDiv = document.getElementById('landingSignupError');
                const submitBtn = e.target.querySelector('button[type="submit"]');

                // Clear previous errors
                errorDiv.textContent = '';

                // Basic validation
                if (!username || !email || !password) {
                    errorDiv.textContent = 'All fields are required';
                    return;
                }

                if (password.length < 6) {
                    errorDiv.textContent = 'Password must be at least 6 characters';
                    return;
                }

                // Email validation
                if (!validateEmail(email)) {
                    errorDiv.textContent = 'Please enter a valid email address';
                    return;
                }

                // Username validation
                if (!validateUsername(username)) {
                    errorDiv.textContent = 'Username must be 3-20 characters and contain only letters, numbers, and underscores';
                    return;
                }

                // Password validation
                const passwordValidation = validatePassword(password);
                if (!passwordValidation.isValid) {
                    errorDiv.textContent = passwordValidation.errors[0]; // Show first error
                    return;
                }

                // Show loading state
                setFormLoading(e.target, true);
                setButtonLoading(submitBtn, true, 'Creating Account...');

                // Show progress indicator for signup
                const progress = showProgressIndicator('Creating your account...', [
                    'Validating information',
                    'Creating account',
                    'Setting up profile',
                    'Preparing your feed'
                ]);

                try {
                    progress.updateStep(0);
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // Use Firebase authentication for signup
                    const result = await window.FirebaseAuth.signUp(email, password, username);

                    if (result.success) {
                        progress.updateStep(1);
                        await new Promise(resolve => setTimeout(resolve, 300));

                        // Create user profile in backend
                        const profileData = await safeFetch('/api/register', {
                            method: 'POST',
                            body: JSON.stringify({
                                uid: result.user.uid,
                                username: username,
                                email: email
                            })
                        });

                        if (profileData.success) {
                            progress.updateStep(2);
                            await new Promise(resolve => setTimeout(resolve, 300));

                            progress.updateStep(3);
                            await new Promise(resolve => setTimeout(resolve, 300));

                            progress.close();

                            // Show enhanced success message
                            showSuccessMessageWithAction(
                                'Account created successfully! Welcome to Narrate!',
                                'Create your first post',
                                () => document.getElementById('storyTitleInput').focus()
                            );
                        } else {
                            progress.close();
                            errorDiv.textContent = profileData.error || 'Failed to create user profile';
                        }
                    } else {
                        progress.close();
                        errorDiv.textContent = result.error || 'Registration failed. Please try again.';
                    }
                } catch (error) {
                    progress.close();
                    const errorInfo = handleError(error, 'Landing page signup', false);
                    errorDiv.textContent = errorInfo.message;
                } finally {
                    // Reset loading state
                    setFormLoading(e.target, false);
                    setButtonLoading(submitBtn, false);
                }
            });

            // Landing page login form
            document.getElementById('landingLoginForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                const email = document.getElementById('landingLoginEmail').value.trim();
                const password = document.getElementById('landingLoginPassword').value;
                const errorDiv = document.getElementById('landingLoginError');
                const submitBtn = e.target.querySelector('button[type="submit"]');

                // Clear previous errors
                errorDiv.textContent = '';

                // Basic validation
                if (!email || !password) {
                    errorDiv.textContent = 'Email and password are required';
                    return;
                }

                // Email validation
                if (!validateEmail(email)) {
                    errorDiv.textContent = 'Please enter a valid email address';
                    return;
                }

                // Show loading state
                setFormLoading(e.target, true);
                setButtonLoading(submitBtn, true, 'Signing In...');

                try {
                    // Use Firebase authentication
                    const result = await window.FirebaseAuth.signIn(email, password);

                    if (result.success) {
                        // Firebase auth state change will handle the rest
                        showSuccessMessage(`Welcome back, ${result.user.username}!`);
                    } else {
                        errorDiv.textContent = result.error || 'Login failed. Please try again.';
                    }
                } catch (error) {
                    const errorInfo = handleError(error, 'Landing page login', false);
                    errorDiv.textContent = errorInfo.message;
                } finally {
                    // Reset loading state
                    setFormLoading(e.target, false);
                    setButtonLoading(submitBtn, false);
                }
            });

            // --- Modal Authentication (kept for backward compatibility) ---
            document.getElementById('loginBtn').addEventListener('click', async () => {
                const email = document.getElementById('loginEmail').value.trim();
                const password = document.getElementById('loginPassword').value;
                const errorDiv = document.getElementById('loginError');
                const submitBtn = document.getElementById('loginBtn');

                // Clear previous errors
                errorDiv.textContent = '';

                // Basic validation
                if (!email || !password) {
                    errorDiv.textContent = 'Email and password are required';
                    return;
                }

                // Show loading state
                setButtonLoading(submitBtn, true, 'Signing In...');

                try {
                    // Use Firebase authentication
                    const result = await window.FirebaseAuth.signIn(email, password);

                    if (result.success) {
                        closeAuthModal();
                        showSuccessMessage(`Welcome back, ${result.user.username}!`);
                        // Firebase auth state change will handle the rest
                    } else {
                        errorDiv.textContent = result.error || 'Login failed. Please try again.';
                    }
                } catch (error) {
                    const errorInfo = handleError(error, 'Modal login', false);
                    errorDiv.textContent = errorInfo.message;
                } finally {
                    setButtonLoading(submitBtn, false);
                }
            });

            document.getElementById('signupBtn').addEventListener('click', async () => {
                const email = document.getElementById('signupEmail').value.trim();
                const username = document.getElementById('signupUsername').value.trim();
                const password = document.getElementById('signupPassword').value;
                const errorDiv = document.getElementById('signupError');
                const submitBtn = document.getElementById('signupBtn');

                // Clear previous errors
                errorDiv.textContent = '';

                // Basic validation
                if (!email || !username || !password) {
                    errorDiv.textContent = 'All fields are required';
                    return;
                }

                // Show loading state
                setButtonLoading(submitBtn, true, 'Creating Account...');

                try {
                    // Use Firebase authentication for signup
                    const result = await window.FirebaseAuth.signUp(email, password, username);

                    if (result.success) {
                        // Create user profile in backend
                        const profileData = await safeFetch('/api/register', {
                            method: 'POST',
                            body: JSON.stringify({
                                uid: result.user.uid,
                                username: username,
                                email: email
                            })
                        });

                        if (profileData.success) {
                            closeAuthModal();
                            showSuccessMessage('Account created successfully! Welcome to Narrate!');
                            // Firebase auth state change will handle the rest
                        } else {
                            errorDiv.textContent = profileData.error || 'Failed to create user profile';
                        }
                    } else {
                        errorDiv.textContent = result.error || 'Registration failed. Please try again.';
                    }
                } catch (error) {
                    const errorInfo = handleError(error, 'Modal signup', false);
                    errorDiv.textContent = errorInfo.message;
                } finally {
                    setButtonLoading(submitBtn, false);
                }
            });

            // --- Post Creation ---
            document.getElementById('postBtn').addEventListener('click', async () => {
                // Protect post creation with authentication check
                requireAuth('createPost', async () => {
                    const title = document.getElementById('storyTitleInput').value.trim();
                    const content = document.getElementById('storyContentInput').value.trim();
                    const category = document.getElementById('storyCategorySelect')?.value || 'Personal Experience';
                    const postBtn = document.getElementById('postBtn');

                    // Check rate limiting
                    if (!SecurityUtils.checkRateLimit('createPost', 5, 60000)) {
                        showErrorMessage('You are posting too frequently. Please wait a moment before posting again.');
                        return;
                    }

                    // Validate post content
                    const validation = validatePostContent(title, content);
                    if (!validation.isValid) {
                        showErrorMessage(validation.errors[0]);
                        return;
                    }

                    try {
                        // Show loading state
                        setButtonLoading(postBtn, true, 'Sharing...');

                        // Create post via API
                        const response = await safeFetch('/api/posts', {
                            method: 'POST',
                            body: JSON.stringify({
                                title,
                                content,
                                category,
                                userId: currentUser.id,
                                username: currentUser.username
                            })
                        });

                        if (response.success) {
                            // Clear the form
                            document.getElementById('storyTitleInput').value = '';
                            document.getElementById('storyContentInput').value = '';
                            document.getElementById('titleSuggestionsContainer').classList.add('hidden');
                            const categorySelect = document.getElementById('storyCategorySelect');
                            if (categorySelect) {
                                categorySelect.value = 'Personal Experience';
                            }

                            // Reload posts to show the new one
                            await loadPosts();

                            showSuccessMessage('Your story has been shared with the community!');
                        } else {
                            showErrorMessage(response.error || 'Failed to share your story. Please try again.');
                        }
                    } catch (error) {
                        const errorInfo = handleError(error, 'Creating post', false);
                        showErrorMessage(errorInfo.message);
                    } finally {
                        setButtonLoading(postBtn, false);
                    }
                });
            });

            // --- Load and Display Posts ---
            async function loadPosts() {
                const startTime = performance.now();
                const postsContainer = document.getElementById('postsContainer');

                // Show skeleton loading
                showPostsSkeleton();

                try {
                    // Fetch posts from API
                    const posts = await safeFetch('/api/posts');

                    if (!posts || posts.length === 0) {
                        postsContainer.innerHTML = `
                            <div class="bg-white p-8 rounded-xl border border-slate-200 shadow-sm text-center">
                                <div class="text-center">
                                    <i data-lucide="file-text" class="w-12 h-12 text-slate-300 mx-auto mb-4"></i>
                                    <h3 class="text-lg font-semibold text-slate-600 mb-2">No stories yet</h3>
                                    <p class="text-slate-500">Be the first to share your story with the community!</p>
                                </div>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    postsContainer.innerHTML = posts.map(createPostHTML).join('');
                    lucide.createIcons();
                    attachCommentListeners();

                    // Initialize lazy loading for new images
                    postsContainer.querySelectorAll('img.lazy-load').forEach(img => {
                        const src = img.dataset.src;
                        if (src) {
                            makeLazyLoadable(img, src);
                        }
                    });

                    // Enable mobile gestures on new posts
                    if (window.innerWidth <= 768) {
                        setupPostSwipeActions();
                        setupTouchFeedback();
                    }
                } catch (error) {
                    const errorInfo = handleError(error, 'Loading posts', false);
                    postsContainer.innerHTML = `
                        <div class="bg-white p-8 rounded-xl border border-slate-200 shadow-sm text-center">
                            <div class="text-center">
                                <i data-lucide="alert-circle" class="w-12 h-12 text-red-300 mx-auto mb-4"></i>
                                <h3 class="text-lg font-semibold text-slate-600 mb-2">Unable to load stories</h3>
                                <p class="text-slate-500 mb-4">${errorInfo.message}</p>
                                <button onclick="loadPosts()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                                    Try Again
                                </button>
                            </div>
                        </div>
                    `;
                    lucide.createIcons();
                } finally {
                    // Record performance metrics
                    performanceMetrics.recordLoadTime('loadPosts', startTime);
                }
            }

            function createPostHTML(post) {
                const timeAgo = getTimeAgo(new Date(post.createdAt));
                const userInitials = post.username.substring(0, 2).toUpperCase();
                const avatarSrc = `https://placehold.co/48x48/E2E8F0/475569?text=${userInitials}`;

                // Escape HTML to prevent XSS
                const escapeHtml = (text) => {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                };

                const safeTitle = escapeHtml(post.title);
                const safeContent = escapeHtml(post.content);
                const safeUsername = escapeHtml(post.username);
                const safeCategory = escapeHtml(post.category || 'General');

                return `
                    <article class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm hover-lift transition-all" data-story-title="${safeTitle}" data-story-content="${safeContent}" data-post-id="${post.id}">
                        <div class="flex items-center mb-4">
                            <img data-src="${avatarSrc}" alt="${safeUsername}" class="w-12 h-12 rounded-full lazy-load bg-slate-100">
                            <div class="ml-4">
                                <h3 class="font-bold text-slate-900">${safeUsername}</h3>
                                <p class="text-sm text-slate-500">${timeAgo} &middot; <span class="font-semibold text-indigo-600">${safeCategory}</span></p>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold mb-2">${safeTitle}</h2>
                        <p class="text-slate-600 leading-relaxed">${safeContent}</p>
                        ${post.image ? `<img data-src="${post.image}" alt="Post image" class="w-full h-64 object-cover rounded-lg mt-4 border border-slate-200 lazy-load bg-slate-100">` : ''}
                        <div class="mt-4 pt-4 border-t border-slate-200 flex justify-around">
                            <button class="like-btn flex items-center gap-2 text-slate-500 hover:text-red-500 font-medium transition-colors" onclick="handleLikeClick(this)">
                                <i data-lucide="heart" class="w-5 h-5"></i> ${post.likes || 0}
                            </button>
                            <button class="comment-btn flex items-center gap-2 text-slate-500 hover:text-sky-500 font-medium transition-colors" onclick="handleCommentClick(this)">
                                <i data-lucide="message-circle" class="w-5 h-5"></i> ${post.comments || 0}
                            </button>
                            <button class="share-btn flex items-center gap-2 text-slate-500 hover:text-emerald-500 font-medium transition-colors" onclick="handleShareClick(this)">
                                <i data-lucide="repeat-2" class="w-5 h-5"></i> ${post.shares || 0}
                            </button>
                            <button class="bookmark-btn flex items-center gap-2 text-slate-500 hover:text-indigo-500 font-medium transition-colors" onclick="handleBookmarkClick(this)">
                                <i data-lucide="bookmark" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </article>
                `;
            }

            function getTimeAgo(date) {
                const now = new Date();
                const diffInSeconds = Math.floor((now - date) / 1000);
                if (diffInSeconds < 60) return 'Just now';
                if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
                if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
                return `${Math.floor(diffInSeconds / 86400)}d ago`;
            }

            // --- Load and Display Suggested Users ---
            async function loadSuggestedUsers() {
                const suggestedUsersContainer = document.getElementById('suggestedUsers');

                // Show skeleton loading
                showUsersSkeleton();

                try {
                    // Simulate API loading delay
                    await new Promise(resolve => setTimeout(resolve, 600));

                    const users = await safeFetch('/api/users');

                    // Filter out current user and limit to 3 suggestions
                    const suggestions = users
                        .filter(user => !currentUser || user.id !== currentUser.id)
                        .slice(0, 3);

                    if (suggestions.length === 0) {
                        suggestedUsersContainer.innerHTML = `
                            <div class="text-center text-slate-500 text-sm">
                                <p>No users to suggest yet</p>
                            </div>
                        `;
                        return;
                    }

                    suggestedUsersContainer.innerHTML = suggestions.map(user => createUserSuggestionHTML(user)).join('');

                    // Attach follow button listeners
                    attachFollowListeners();

                } catch (error) {
                    handleError(error, 'Loading suggested users');
                    suggestedUsersContainer.innerHTML = `
                        <div class="text-center text-slate-500 text-sm p-4">
                            <i data-lucide="users" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                            <p>Unable to load user suggestions</p>
                            <button onclick="loadSuggestedUsers()" class="text-indigo-600 hover:text-indigo-800 text-xs mt-1">
                                Try again
                            </button>
                        </div>
                    `;
                    lucide.createIcons();
                }
            }

            // --- Create User Suggestion HTML ---
            function createUserSuggestionHTML(user) {
                const userInitials = user.username.substring(0, 2).toUpperCase();
                const isFollowing = currentUser && currentUser.followingList && currentUser.followingList.includes(user.id);

                return `
                    <div class="flex items-center gap-3" data-user-id="${user.id}">
                        <img src="https://placehold.co/40x40/A5B4FC/312E81?text=${userInitials}" alt="${user.username}" class="w-10 h-10 rounded-full">
                        <div class="flex-grow">
                            <p class="font-semibold">${user.username}</p>
                            <p class="text-sm text-slate-500">${user.bio || 'New to the community'}</p>
                        </div>
                        <button class="follow-btn ${isFollowing ? 'bg-slate-200 text-slate-600' : 'bg-indigo-600 text-white'} font-semibold px-3 py-1 rounded-full text-sm hover:opacity-80 transition"
                                data-user-id="${user.id}">
                            ${isFollowing ? 'Following' : 'Follow'}
                        </button>
                    </div>
                `;
            }

            // --- Follow Functionality ---
            function attachFollowListeners() {
                document.querySelectorAll('.follow-btn').forEach(button => {
                    button.addEventListener('click', async (e) => {
                        // Protect follow functionality with authentication check
                        if (!requireAuth('followUser')) {
                            return;
                        }

                        const targetUserId = button.dataset.userId;
                        const isCurrentlyFollowing = button.textContent.trim() === 'Following';

                        button.disabled = true;
                        button.textContent = isCurrentlyFollowing ? 'Unfollowing...' : 'Following...';

                        try {
                            const response = await fetch(`/api/users/${targetUserId}/follow`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ userId: currentUser.id })
                            });

                            const result = await response.json();

                            if (result.success) {
                                // Update button appearance
                                if (result.following) {
                                    button.textContent = 'Following';
                                    button.className = 'follow-btn bg-slate-200 text-slate-600 font-semibold px-3 py-1 rounded-full text-sm hover:opacity-80 transition';
                                } else {
                                    button.textContent = 'Follow';
                                    button.className = 'follow-btn bg-indigo-600 text-white font-semibold px-3 py-1 rounded-full text-sm hover:opacity-80 transition';
                                }

                                // Update current user's following count
                                currentUser.following = result.following_count;
                                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                                updateUIForUser();

                            } else {
                                alert('Failed to follow user: ' + (result.error || 'Unknown error'));
                                button.textContent = isCurrentlyFollowing ? 'Following' : 'Follow';
                            }
                        } catch (error) {
                            const errorInfo = handleError(error, 'Follow/unfollow user', false);
                            showErrorMessage(errorInfo.message);
                            button.textContent = isCurrentlyFollowing ? 'Following' : 'Follow';
                        } finally {
                            button.disabled = false;
                        }
                    });
                });
            }

            loadSuggestedUsers();

            // --- Image Upload Functionality ---
            const imageUpload = document.getElementById('imageUpload');
            const imageUploadBtn = document.getElementById('imageUploadBtn');
            const imagePreview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            const removeImageBtn = document.getElementById('removeImageBtn');
            let selectedImage = null;

            imageUploadBtn.addEventListener('click', () => {
                imageUpload.click();
            });

            imageUpload.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    if (file.size > 5 * 1024 * 1024) { // 5MB limit
                        alert('Image size must be less than 5MB');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        previewImg.src = e.target.result;
                        imagePreview.classList.remove('hidden');
                        selectedImage = file;
                    };
                    reader.readAsDataURL(file);
                }
            });

            removeImageBtn.addEventListener('click', () => {
                imagePreview.classList.add('hidden');
                imageUpload.value = '';
                selectedImage = null;
            });



            // --- Search Functionality ---
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');
            let searchTimeout;

            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();

                // Clear previous timeout
                clearTimeout(searchTimeout);

                if (query.length < 2) {
                    searchResults.classList.add('hidden');
                    return;
                }

                // Debounce search requests
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            });

            // Hide search results when clicking outside
            document.addEventListener('click', (e) => {
                if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                    searchResults.classList.add('hidden');
                }
            });

            async function performSearch(query) {
                try {
                    searchResults.innerHTML = '<div class="p-4 text-slate-500 text-sm">Searching...</div>';
                    searchResults.classList.remove('hidden');

                    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
                    const results = await response.json();

                    if (results.error) {
                        searchResults.innerHTML = `<div class="p-4 text-red-500 text-sm">${results.error}</div>`;
                        return;
                    }

                    const hasResults = (results.users && results.users.length > 0) || (results.posts && results.posts.length > 0);

                    if (!hasResults) {
                        searchResults.innerHTML = `
                            <div class="p-4 text-center">
                                <i data-lucide="search-x" class="w-8 h-8 text-slate-300 mx-auto mb-2"></i>
                                <p class="text-slate-500 text-sm">No results found for "${query}"</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    let html = '';

                    // Display user results
                    if (results.users && results.users.length > 0) {
                        html += '<div class="p-3 border-b border-slate-200"><h4 class="font-semibold text-slate-700 text-sm">People</h4></div>';
                        results.users.forEach(user => {
                            const userInitials = user.username.substring(0, 2).toUpperCase();
                            html += `
                                <div class="p-3 hover:bg-slate-50 cursor-pointer border-b border-slate-100" onclick="viewUserProfile('${user.id}')">
                                    <div class="flex items-center gap-3">
                                        <img src="https://placehold.co/32x32/E2E8F0/475569?text=${userInitials}" alt="${user.username}" class="w-8 h-8 rounded-full">
                                        <div>
                                            <p class="font-semibold text-sm">${user.username}</p>
                                            <p class="text-xs text-slate-500">${user.bio || 'New to the community'}</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                    }

                    // Display post results
                    if (results.posts && results.posts.length > 0) {
                        html += '<div class="p-3 border-b border-slate-200"><h4 class="font-semibold text-slate-700 text-sm">Stories</h4></div>';
                        results.posts.forEach(post => {
                            const timeAgo = getTimeAgo(new Date(post.createdAt));
                            html += `
                                <div class="p-3 hover:bg-slate-50 cursor-pointer border-b border-slate-100" onclick="viewPost('${post.id}')">
                                    <div>
                                        <p class="font-semibold text-sm line-clamp-1">${post.title}</p>
                                        <p class="text-xs text-slate-500 mb-1">by ${post.username} • ${timeAgo}</p>
                                        <p class="text-xs text-slate-600 line-clamp-2">${post.content}</p>
                                    </div>
                                </div>
                            `;
                        });
                    }

                    searchResults.innerHTML = html;
                    lucide.createIcons();

                } catch (error) {
                    console.error('Search error:', error);
                    searchResults.innerHTML = '<div class="p-4 text-red-500 text-sm">Search failed. Please try again.</div>';
                }
            }

            // Search result click handlers
            window.viewUserProfile = function(userId) {
                searchResults.classList.add('hidden');
                searchInput.value = '';
                alert(`User profile feature coming soon! User ID: ${userId}`);
            };

            window.viewPost = function(postId) {
                searchResults.classList.add('hidden');
                searchInput.value = '';
                // Scroll to the post if it's visible on the page
                const postElement = document.querySelector(`[data-post-id="${postId}"]`);
                if (postElement) {
                    postElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    postElement.classList.add('ring-2', 'ring-indigo-300');
                    setTimeout(() => {
                        postElement.classList.remove('ring-2', 'ring-indigo-300');
                    }, 3000);
                } else {
                    alert(`Post details feature coming soon! Post ID: ${postId}`);
                }
            };

            // --- Notifications System ---
            const notificationBtn = document.getElementById('notificationBtn');
            const notificationDropdown = document.getElementById('notificationDropdown');
            const notificationBadge = document.getElementById('notificationBadge');
            const notificationsList = document.getElementById('notificationsList');
            const markAllReadBtn = document.getElementById('markAllReadBtn');
            let unreadCount = 0;

            // Toggle notification dropdown
            notificationBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                // Protect notifications functionality with authentication check
                requireAuth('viewNotifications', () => {
                    notificationDropdown.classList.toggle('hidden');
                    if (!notificationDropdown.classList.contains('hidden')) {
                        loadNotifications();
                    }
                });
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!notificationBtn.contains(e.target) && !notificationDropdown.contains(e.target)) {
                    notificationDropdown.classList.add('hidden');
                }
            });

            // Mark all notifications as read
            markAllReadBtn.addEventListener('click', async () => {
                if (!currentUser) return;

                try {
                    const response = await fetch(`/api/notifications/${currentUser.id}/read-all`, {
                        method: 'PUT'
                    });

                    if (response.ok) {
                        loadNotifications();
                        updateNotificationBadge(0);
                    }
                } catch (error) {
                    console.error('Error marking notifications as read:', error);
                }
            });

            // Load notifications
            async function loadNotifications() {
                if (!currentUser) {
                    notificationsList.innerHTML = '<div class="p-4 text-center text-slate-500">Please login to see notifications</div>';
                    return;
                }

                try {
                    const response = await fetch(`/api/notifications/${currentUser.id}`);
                    const notifications = await response.json();

                    if (notifications.length === 0) {
                        notificationsList.innerHTML = `
                            <div class="p-8 text-center">
                                <i data-lucide="bell-off" class="w-12 h-12 text-slate-300 mx-auto mb-3"></i>
                                <p class="text-slate-500">No notifications yet</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    notificationsList.innerHTML = notifications.map(notification => createNotificationHTML(notification)).join('');
                    lucide.createIcons();

                    // Update unread count
                    const unreadNotifications = notifications.filter(n => !n.read);
                    updateNotificationBadge(unreadNotifications.length);

                } catch (error) {
                    console.error('Error loading notifications:', error);
                    notificationsList.innerHTML = '<div class="p-4 text-center text-red-500">Failed to load notifications</div>';
                }
            }

            // Create notification HTML
            function createNotificationHTML(notification) {
                const timeAgo = getTimeAgo(new Date(notification.createdAt));
                const isUnread = !notification.read;

                return `
                    <div class="p-4 hover:bg-slate-50 cursor-pointer ${isUnread ? 'bg-indigo-50' : ''}"
                         onclick="markNotificationRead('${notification.id}')">
                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0">
                                ${getNotificationIcon(notification.type)}
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-slate-900 ${isUnread ? 'font-semibold' : ''}">${notification.message}</p>
                                <p class="text-xs text-slate-500 mt-1">${timeAgo}</p>
                            </div>
                            ${isUnread ? '<div class="w-2 h-2 bg-indigo-500 rounded-full flex-shrink-0 mt-2"></div>' : ''}
                        </div>
                    </div>
                `;
            }

            // Get notification icon based on type
            function getNotificationIcon(type) {
                const iconMap = {
                    'like': '<i data-lucide="heart" class="w-5 h-5 text-red-500"></i>',
                    'comment': '<i data-lucide="message-circle" class="w-5 h-5 text-blue-500"></i>',
                    'follow': '<i data-lucide="user-plus" class="w-5 h-5 text-green-500"></i>',
                    'mention': '<i data-lucide="at-sign" class="w-5 h-5 text-purple-500"></i>'
                };
                return iconMap[type] || '<i data-lucide="bell" class="w-5 h-5 text-slate-500"></i>';
            }

            // Mark notification as read
            window.markNotificationRead = async function(notificationId) {
                try {
                    const response = await fetch(`/api/notifications/${notificationId}/read`, {
                        method: 'PUT'
                    });

                    if (response.ok) {
                        loadNotifications();
                    }
                } catch (error) {
                    console.error('Error marking notification as read:', error);
                }
            };

            // Update notification badge
            function updateNotificationBadge(count) {
                unreadCount = count;
                if (count > 0) {
                    notificationBadge.textContent = count > 99 ? '99+' : count;
                    notificationBadge.classList.remove('hidden');
                } else {
                    notificationBadge.classList.add('hidden');
                }

                // Also update mobile notification badge
                updateMobileNotificationBadge(count);
            }

            // Load notifications periodically (every 30 seconds)
            setInterval(() => {
                if (currentUser && notificationDropdown.classList.contains('hidden')) {
                    // Only update badge when dropdown is closed to avoid disrupting user
                    fetch(`/api/notifications/${currentUser.id}`)
                        .then(response => response.json())
                        .then(notifications => {
                            const unreadNotifications = notifications.filter(n => !n.read);
                            updateNotificationBadge(unreadNotifications.length);
                        })
                        .catch(error => console.error('Error checking notifications:', error));
                }
            }, 30000);

            // --- Direct Messaging System ---
            const messagesBtn = document.getElementById('messagesBtn');
            const messagesModal = document.getElementById('messagesModal');
            const closeMessagesBtn = document.getElementById('closeMessagesBtn');
            const conversationsList = document.getElementById('conversationsList');
            const chatHeader = document.getElementById('chatHeader');
            const chatMessages = document.getElementById('chatMessages');
            const chatInput = document.getElementById('chatInput');
            const messageInput = document.getElementById('messageInput');
            const sendMessageBtn = document.getElementById('sendMessageBtn');
            let currentChatPartner = null;

            // Open messages modal
            messagesBtn.addEventListener('click', () => {
                // Protect messaging functionality with authentication check
                requireAuth('sendMessage', () => {
                    messagesModal.classList.remove('hidden');
                    loadConversations();
                });
            });

            // Close messages modal
            closeMessagesBtn.addEventListener('click', () => {
                messagesModal.classList.add('hidden');
                currentChatPartner = null;
                resetChatView();
            });

            // Load conversations
            async function loadConversations() {
                if (!currentUser) return;

                try {
                    conversationsList.innerHTML = '<div class="p-4 text-center text-slate-500">Loading...</div>';

                    const response = await fetch(`/api/conversations/${currentUser.id}`);
                    const conversations = await response.json();

                    if (conversations.length === 0) {
                        conversationsList.innerHTML = `
                            <div class="p-8 text-center">
                                <i data-lucide="message-circle-plus" class="w-12 h-12 text-slate-300 mx-auto mb-3"></i>
                                <p class="text-slate-500 text-sm">No conversations yet</p>
                                <p class="text-slate-400 text-xs">Start a conversation by visiting someone's profile</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    conversationsList.innerHTML = conversations.map(conversation => createConversationHTML(conversation)).join('');
                    lucide.createIcons();

                } catch (error) {
                    console.error('Error loading conversations:', error);
                    conversationsList.innerHTML = '<div class="p-4 text-center text-red-500">Failed to load conversations</div>';
                }
            }

            // Create conversation HTML
            function createConversationHTML(conversation) {
                const partnerInitials = conversation.partnerName.substring(0, 2).toUpperCase();
                const timeAgo = getTimeAgo(new Date(conversation.lastMessage.createdAt));

                return `
                    <div class="p-4 hover:bg-slate-50 cursor-pointer conversation-item"
                         onclick="openChat('${conversation.partnerId}', '${conversation.partnerName}', '${conversation.partnerBio}')">
                        <div class="flex items-center gap-3">
                            <div class="relative">
                                <img src="https://placehold.co/40x40/E2E8F0/475569?text=${partnerInitials}"
                                     alt="${conversation.partnerName}" class="w-10 h-10 rounded-full">
                                ${conversation.unreadCount > 0 ? `<span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">${conversation.unreadCount}</span>` : ''}
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex justify-between items-start">
                                    <h4 class="font-semibold text-sm truncate">${conversation.partnerName}</h4>
                                    <span class="text-xs text-slate-500">${timeAgo}</span>
                                </div>
                                <p class="text-sm text-slate-600 truncate">
                                    ${conversation.lastMessage.isFromMe ? 'You: ' : ''}${conversation.lastMessage.content}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Open chat with specific user
            window.openChat = async function(partnerId, partnerName, partnerBio) {
                currentChatPartner = partnerId;

                // Update chat header
                const partnerInitials = partnerName.substring(0, 2).toUpperCase();
                document.getElementById('chatPartnerAvatar').src = `https://placehold.co/40x40/E2E8F0/475569?text=${partnerInitials}`;
                document.getElementById('chatPartnerName').textContent = partnerName;
                document.getElementById('chatPartnerBio').textContent = partnerBio || 'New to the community';

                chatHeader.classList.remove('hidden');
                chatInput.classList.remove('hidden');

                // Load messages
                await loadChatMessages(partnerId);

                // Mark messages as read
                try {
                    await fetch(`/api/messages/${currentUser.id}/${partnerId}/read`, {
                        method: 'PUT'
                    });
                    loadConversations(); // Refresh to update unread counts
                } catch (error) {
                    console.error('Error marking messages as read:', error);
                }
            };

            // Load chat messages
            async function loadChatMessages(partnerId) {
                try {
                    const response = await fetch(`/api/messages/${currentUser.id}/${partnerId}`);
                    const messages = await response.json();

                    if (messages.length === 0) {
                        chatMessages.innerHTML = `
                            <div class="flex items-center justify-center h-full text-slate-500">
                                <div class="text-center">
                                    <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-3 text-slate-300"></i>
                                    <p>No messages yet. Start the conversation!</p>
                                </div>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    chatMessages.innerHTML = messages.map(message => createMessageHTML(message)).join('');
                    chatMessages.scrollTop = chatMessages.scrollHeight;

                } catch (error) {
                    console.error('Error loading messages:', error);
                    chatMessages.innerHTML = '<div class="text-center text-red-500">Failed to load messages</div>';
                }
            }

            // Create message HTML
            function createMessageHTML(message) {
                const isFromMe = message.senderId === currentUser.id;
                const timeAgo = getTimeAgo(new Date(message.createdAt));

                return `
                    <div class="flex ${isFromMe ? 'justify-end' : 'justify-start'}">
                        <div class="max-w-xs lg:max-w-md">
                            <div class="px-4 py-2 rounded-lg ${isFromMe ? 'bg-indigo-600 text-white' : 'bg-slate-100 text-slate-900'}">
                                <p class="text-sm">${message.content}</p>
                            </div>
                            <p class="text-xs text-slate-500 mt-1 ${isFromMe ? 'text-right' : 'text-left'}">${timeAgo}</p>
                        </div>
                    </div>
                `;
            }

            // Send message
            async function sendMessage() {
                if (!currentChatPartner || !currentUser) return;

                const content = messageInput.value.trim();
                if (!content) return;

                messageInput.disabled = true;
                sendMessageBtn.disabled = true;

                try {
                    const response = await fetch('/api/messages', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            senderId: currentUser.id,
                            receiverId: currentChatPartner,
                            content: content
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        messageInput.value = '';
                        await loadChatMessages(currentChatPartner);
                        loadConversations(); // Refresh conversations list
                    } else {
                        alert('Failed to send message: ' + (result.error || 'Unknown error'));
                    }

                } catch (error) {
                    const errorInfo = handleError(error, 'Sending message', false);
                    showErrorMessage(errorInfo.message);
                } finally {
                    messageInput.disabled = false;
                    sendMessageBtn.disabled = false;
                }
            }

            // Send message on button click
            sendMessageBtn.addEventListener('click', sendMessage);

            // Send message on Enter key
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Reset chat view
            function resetChatView() {
                chatHeader.classList.add('hidden');
                chatInput.classList.add('hidden');
                chatMessages.innerHTML = `
                    <div class="flex items-center justify-center h-full text-slate-500">
                        <div class="text-center">
                            <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-3 text-slate-300"></i>
                            <p>Select a conversation to start messaging</p>
                        </div>
                    </div>
                `;
                lucide.createIcons();
            }

            // --- Mobile Search Functionality ---
            const mobileSearchBtn = document.getElementById('mobileSearchBtn');
            const mobileSearchModal = document.getElementById('mobileSearchModal');
            const closeMobileSearchBtn = document.getElementById('closeMobileSearchBtn');
            const mobileSearchInput = document.getElementById('mobileSearchInput');
            const mobileSearchResults = document.getElementById('mobileSearchResults');
            let mobileSearchTimeout;

            mobileSearchBtn.addEventListener('click', () => {
                mobileSearchModal.classList.remove('hidden');
                mobileSearchInput.focus();
            });

            closeMobileSearchBtn.addEventListener('click', () => {
                mobileSearchModal.classList.add('hidden');
                mobileSearchInput.value = '';
                mobileSearchResults.innerHTML = '';
            });

            mobileSearchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();

                clearTimeout(mobileSearchTimeout);

                if (query.length < 2) {
                    mobileSearchResults.innerHTML = '';
                    return;
                }

                mobileSearchTimeout = setTimeout(() => {
                    performMobileSearch(query);
                }, 300);
            });

            async function performMobileSearch(query) {
                try {
                    mobileSearchResults.innerHTML = '<div class="p-4 text-slate-500 text-sm">Searching...</div>';

                    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
                    const results = await response.json();

                    if (results.error) {
                        mobileSearchResults.innerHTML = `<div class="p-4 text-red-500 text-sm">${results.error}</div>`;
                        return;
                    }

                    const hasResults = (results.users && results.users.length > 0) || (results.posts && results.posts.length > 0);

                    if (!hasResults) {
                        mobileSearchResults.innerHTML = `
                            <div class="p-8 text-center">
                                <i data-lucide="search-x" class="w-12 h-12 text-slate-300 mx-auto mb-4"></i>
                                <p class="text-slate-500">No results found for "${query}"</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    let html = '';

                    // Display user results
                    if (results.users && results.users.length > 0) {
                        html += '<div class="mb-6"><h4 class="font-semibold text-slate-700 mb-3">People</h4>';
                        results.users.forEach(user => {
                            const userInitials = user.username.substring(0, 2).toUpperCase();
                            html += `
                                <div class="p-3 hover:bg-slate-50 rounded-lg cursor-pointer mb-2" onclick="viewUserProfile('${user.id}')">
                                    <div class="flex items-center gap-3">
                                        <img src="https://placehold.co/40x40/E2E8F0/475569?text=${userInitials}" alt="${user.username}" class="w-10 h-10 rounded-full">
                                        <div>
                                            <p class="font-semibold">${user.username}</p>
                                            <p class="text-sm text-slate-500">${user.bio || 'New to the community'}</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    }

                    // Display post results
                    if (results.posts && results.posts.length > 0) {
                        html += '<div><h4 class="font-semibold text-slate-700 mb-3">Stories</h4>';
                        results.posts.forEach(post => {
                            const timeAgo = getTimeAgo(new Date(post.createdAt));
                            html += `
                                <div class="p-3 hover:bg-slate-50 rounded-lg cursor-pointer mb-2" onclick="viewPost('${post.id}')">
                                    <div>
                                        <p class="font-semibold line-clamp-1">${post.title}</p>
                                        <p class="text-sm text-slate-500 mb-1">by ${post.username} • ${timeAgo}</p>
                                        <p class="text-sm text-slate-600 line-clamp-2">${post.content}</p>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    }

                    mobileSearchResults.innerHTML = html;
                    lucide.createIcons();

                } catch (error) {
                    console.error('Mobile search error:', error);
                    mobileSearchResults.innerHTML = '<div class="p-4 text-red-500 text-sm">Search failed. Please try again.</div>';
                }
            }

            // Gemini API Key - LEAVE BLANK
            const API_KEY = "";

            // --- ✨ AI Story Title Suggester ---
            const storyContentInput = document.getElementById('storyContentInput');
            const suggestTitlesBtn = document.getElementById('suggestTitlesBtn');
            const titleSuggestionsContainer = document.getElementById('titleSuggestionsContainer');
            const storyTitleInput = document.getElementById('storyTitleInput');

            storyContentInput.addEventListener('input', () => {
                suggestTitlesBtn.classList.toggle('hidden', storyContentInput.value.trim().length <= 20);
                suggestTitlesBtn.classList.toggle('flex', storyContentInput.value.trim().length > 20);
            });

            suggestTitlesBtn.addEventListener('click', async () => {
                const storyText = storyContentInput.value;
                if (storyText.length < 20 || !API_KEY) {
                    if(!API_KEY) titleSuggestionsContainer.innerHTML = `<p class="text-sm text-red-500">API Key is not set.</p>`;
                    return;
                };

                suggestTitlesBtn.disabled = true;
                suggestTitlesBtn.innerHTML = '<div class="spinner"></div>';
                titleSuggestionsContainer.classList.remove('hidden');
                titleSuggestionsContainer.innerHTML = '<p class="text-sm text-slate-500">✨ Thinking of some titles...</p>';

                const prompt = `Based on the following story draft, generate 5 creative and engaging title suggestions. Return as a JSON object with a "titles" array. Story: "${storyText}"`;
                const payload = {
                  contents: [{ role: "user", parts: [{ text: prompt }] }],
                  generationConfig: {
                    responseMimeType: "application/json",
                    responseSchema: { type: "OBJECT", properties: { titles: { type: "ARRAY", items: { type: "STRING" } } } }
                  }
                };

                try {
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                    const result = await response.json();
                    const parsedJson = JSON.parse(result.candidates[0].content.parts[0].text);

                    titleSuggestionsContainer.innerHTML = '';
                    parsedJson.titles.forEach(title => {
                        const btn = document.createElement('button');
                        btn.className = 'w-full text-left p-2 bg-slate-100 rounded-md hover:bg-indigo-100 text-slate-700 text-sm';
                        btn.textContent = title;
                        btn.onclick = () => {
                            storyTitleInput.value = title;
                            titleSuggestionsContainer.classList.add('hidden');
                        };
                        titleSuggestionsContainer.appendChild(btn);
                    });
                } catch (error) {
                    titleSuggestionsContainer.innerHTML = `<p class="text-sm text-red-500">Could not fetch suggestions. ${error.message}</p>`;
                } finally {
                    suggestTitlesBtn.disabled = false;
                    suggestTitlesBtn.innerHTML = '✨ Suggest Titles';
                }
            });

            // --- ✨ AI Bio Generator ---
            document.querySelectorAll('.generate-bio-btn').forEach(button => {
                button.addEventListener('click', async (e) => {
                    if (!API_KEY) return;
                    const card = e.currentTarget.closest('[data-person-name]');
                    const name = card.dataset.personName;
                    const role = card.dataset.personRole;
                    const bioTextElement = card.querySelector('.bio-text');
                    const originalText = bioTextElement.textContent;
                    button.disabled = true;
                    button.innerHTML = '<div class="spinner !w-5 !h-5"></div>';

                    const prompt = `Generate a short, one-sentence, friendly, and hypothetical bio for a person named ${name} who is a ${role}. Make it sound like a profile summary on a social network.`;
                    const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

                    try {
                         const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });
                        if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                        const result = await response.json();
                        bioTextElement.textContent = result.candidates[0].content.parts[0].text;
                        button.classList.add('hidden');
                    } catch (error) {
                        bioTextElement.textContent = originalText;
                        button.disabled = false;
                        button.innerHTML = '✨';
                    }
                });
            });

            // --- Comment Modal & ✨ AI Comment Generator ---
            const modal = document.getElementById('commentModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const modalStoryTitle = document.getElementById('modalStoryTitle');
            const commentTextarea = document.getElementById('commentTextarea');
            const generateCommentBtn = document.getElementById('generateCommentBtn');
            const commentError = document.getElementById('commentError');

            function attachCommentListeners() {
                document.querySelectorAll('.comment-btn').forEach(button => {
                    button.addEventListener('click', async (e) => {
                        const article = e.currentTarget.closest('article');
                        const title = article.dataset.storyTitle;
                        const content = article.dataset.storyContent;

                        // Get post ID from the like button
                        const likeBtn = article.querySelector('.like-btn');
                        const postId = likeBtn ? likeBtn.dataset.postId : null;

                        modalStoryTitle.textContent = title;
                        modal.dataset.storyTitle = title;
                        modal.dataset.storyContent = content;
                        modal.dataset.postId = postId;
                        commentTextarea.value = '';
                        commentError.textContent = '';

                        // Load existing comments
                        await loadComments(postId);

                        modal.classList.remove('hidden');
                    });
                });
            }

            // --- Load Comments Function ---
            async function loadComments(postId) {
                const existingCommentsContainer = document.getElementById('existingComments');

                if (!postId) {
                    existingCommentsContainer.innerHTML = '<p class="text-slate-500 text-sm">Unable to load comments</p>';
                    return;
                }

                try {
                    existingCommentsContainer.innerHTML = '<p class="text-slate-500 text-sm">Loading comments...</p>';

                    const response = await fetch(`/api/posts/${postId}/comments`);
                    const comments = await response.json();

                    if (comments.length === 0) {
                        existingCommentsContainer.innerHTML = `
                            <div class="text-center py-8">
                                <i data-lucide="message-circle" class="w-12 h-12 text-slate-300 mx-auto mb-3"></i>
                                <p class="text-slate-500">No comments yet</p>
                                <p class="text-slate-400 text-sm">Be the first to share your thoughts!</p>
                            </div>
                        `;
                        lucide.createIcons();
                        return;
                    }

                    existingCommentsContainer.innerHTML = comments.map(comment => createCommentHTML(comment)).join('');
                    lucide.createIcons();

                } catch (error) {
                    console.error('Error loading comments:', error);
                    existingCommentsContainer.innerHTML = '<p class="text-red-500 text-sm">Failed to load comments</p>';
                }
            }

            // --- Create Comment HTML ---
            function createCommentHTML(comment) {
                const timeAgo = getTimeAgo(new Date(comment.createdAt));
                const userInitials = comment.username.substring(0, 2).toUpperCase();

                return `
                    <div class="flex gap-3 p-3 bg-slate-50 rounded-lg">
                        <img src="https://placehold.co/32x32/E2E8F0/475569?text=${userInitials}" alt="${comment.username}" class="w-8 h-8 rounded-full flex-shrink-0">
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-1">
                                <span class="font-semibold text-sm">${comment.username}</span>
                                <span class="text-xs text-slate-500">${timeAgo}</span>
                            </div>
                            <p class="text-slate-700 text-sm leading-relaxed">${comment.content}</p>
                        </div>
                    </div>
                `;
            }

            const closeModal = () => modal.classList.add('hidden');
            closeModalBtn.addEventListener('click', closeModal);
            modal.addEventListener('click', (e) => e.target === modal && closeModal());

            generateCommentBtn.addEventListener('click', async () => {
                if (!API_KEY) {
                    commentError.textContent = 'API Key is not set.';
                    return;
                }
                const title = modal.dataset.storyTitle;
                const content = modal.dataset.storyContent;
                generateCommentBtn.disabled = true;
                generateCommentBtn.innerHTML = '<div class="spinner"></div> Generating...';
                commentError.textContent = '';

                const prompt = `Generate a short, insightful, and friendly comment for a story titled "${title}". The story content is: "${content}"`;
                const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

                try {
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                    const result = await response.json();
                    commentTextarea.value = result.candidates[0].content.parts[0].text;
                } catch (error) {
                    commentError.textContent = `Failed to generate comment. ${error.message}`;
                } finally {
                    generateCommentBtn.disabled = false;
                    generateCommentBtn.innerHTML = '✨ Generate Comment';
                }
            });

            // --- Post Comment Functionality ---
            const postCommentBtn = document.getElementById('postCommentBtn');

            postCommentBtn.addEventListener('click', async () => {
                // Protect comment functionality with authentication check
                if (!requireAuth('commentPost')) {
                    return;
                }

                const content = commentTextarea.value.trim();
                if (!content) {
                    commentError.textContent = 'Please enter a comment';
                    return;
                }

                const postId = modal.dataset.postId;
                if (!postId) {
                    commentError.textContent = 'Could not identify the post';
                    return;
                }

                postCommentBtn.disabled = true;
                postCommentBtn.textContent = 'Posting...';
                commentError.textContent = '';

                try {
                    const response = await fetch(`/api/posts/${postId}/comment`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            userId: currentUser.id,
                            username: currentUser.username,
                            content: content
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Clear the form
                        commentTextarea.value = '';

                        // Reload comments to show the new one
                        await loadComments(postId);

                        // Update the comment count in the main feed
                        const article = document.querySelector(`[data-story-title="${modal.dataset.storyTitle}"]`);
                        if (article) {
                            const commentBtn = article.querySelector('.comment-btn');
                            if (commentBtn) {
                                const commentText = commentBtn.innerHTML;
                                commentBtn.innerHTML = commentText.replace(/\d+/, result.comments);
                            }
                        }

                    } else {
                        commentError.textContent = result.error || 'Failed to post comment';
                    }
                } catch (error) {
                    const errorInfo = handleError(error, 'Posting comment', false);
                    commentError.textContent = errorInfo.message;
                } finally {
                    postCommentBtn.disabled = false;
                    postCommentBtn.textContent = 'Post Comment';
                }
            });
        });

        // --- Mobile Gesture System ---
        let touchStartY = 0;
        let touchStartX = 0;
        let touchCurrentY = 0;
        let touchCurrentX = 0;
        let isRefreshing = false;
        let isSwipeActive = false;
        let currentSwipePost = null;

        // Pull-to-refresh functionality
        const pullRefreshIndicator = document.getElementById('pullRefreshIndicator');
        const mainFeed = document.getElementById('mainFeed');

        // Initialize mobile gestures
        function initializeMobileGestures() {
            // Only enable on mobile devices
            if (window.innerWidth <= 768) {
                setupPullToRefresh();
                setupPostSwipeActions();
                setupTouchFeedback();
            }

            // Re-initialize on window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth <= 768) {
                    setupPullToRefresh();
                    setupPostSwipeActions();
                    setupTouchFeedback();
                } else {
                    removeMobileGestures();
                }
            });
        }

        // Setup pull-to-refresh
        function setupPullToRefresh() {
            if (!mainFeed || !pullRefreshIndicator) return;

            mainFeed.addEventListener('touchstart', handlePullStart, { passive: false });
            mainFeed.addEventListener('touchmove', handlePullMove, { passive: false });
            mainFeed.addEventListener('touchend', handlePullEnd, { passive: false });
        }

        function handlePullStart(e) {
            if (isRefreshing || window.scrollY > 0) return;
            touchStartY = e.touches[0].clientY;
        }

        function handlePullMove(e) {
            if (isRefreshing || window.scrollY > 0) return;

            touchCurrentY = e.touches[0].clientY;
            const pullDistance = touchCurrentY - touchStartY;

            if (pullDistance > 0 && pullDistance < 120) {
                e.preventDefault();
                const progress = Math.min(pullDistance / 80, 1);
                pullRefreshIndicator.style.transform = `translateX(-50%) translateY(${pullDistance * 0.5}px) rotate(${progress * 180}deg)`;
                pullRefreshIndicator.style.opacity = progress;

                if (pullDistance > 60) {
                    pullRefreshIndicator.classList.add('visible');
                } else {
                    pullRefreshIndicator.classList.remove('visible');
                }
            }
        }

        function handlePullEnd(e) {
            if (isRefreshing || window.scrollY > 0) return;

            const pullDistance = touchCurrentY - touchStartY;

            if (pullDistance > 60) {
                triggerRefresh();
            } else {
                resetPullIndicator();
            }
        }

        function triggerRefresh() {
            if (isRefreshing) return;

            isRefreshing = true;
            pullRefreshIndicator.classList.add('visible', 'loading');

            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            // Simulate refresh (reload posts)
            setTimeout(() => {
                loadPosts().then(() => {
                    resetPullIndicator();
                    isRefreshing = false;
                });
            }, 1000);
        }

        function resetPullIndicator() {
            pullRefreshIndicator.style.transform = 'translateX(-50%) translateY(0) rotate(0deg)';
            pullRefreshIndicator.style.opacity = '0';
            pullRefreshIndicator.classList.remove('visible', 'loading');
        }

        // Setup post swipe actions
        function setupPostSwipeActions() {
            // Add swipe listeners to existing posts
            document.querySelectorAll('article').forEach(addSwipeToPost);
        }

        function addSwipeToPost(postElement) {
            if (postElement.dataset.swipeEnabled) return; // Already enabled

            postElement.dataset.swipeEnabled = 'true';
            postElement.addEventListener('touchstart', handlePostSwipeStart, { passive: true });
            postElement.addEventListener('touchmove', handlePostSwipeMove, { passive: false });
            postElement.addEventListener('touchend', handlePostSwipeEnd, { passive: true });
        }

        function handlePostSwipeStart(e) {
            if (isSwipeActive) return;
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            currentSwipePost = e.currentTarget;
        }

        function handlePostSwipeMove(e) {
            if (!currentSwipePost) return;

            touchCurrentX = e.touches[0].clientX;
            touchCurrentY = e.touches[0].clientY;

            const deltaX = touchCurrentX - touchStartX;
            const deltaY = touchCurrentY - touchStartY;

            // Only trigger horizontal swipe if it's more horizontal than vertical
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 30) {
                e.preventDefault();
                isSwipeActive = true;

                if (deltaX < -50) { // Swipe left - show actions
                    showPostSwipeActions(currentSwipePost);
                } else if (deltaX > 50) { // Swipe right - quick like
                    triggerQuickLike(currentSwipePost);
                }
            }
        }

        function handlePostSwipeEnd(e) {
            if (isSwipeActive) {
                setTimeout(() => {
                    isSwipeActive = false;
                    currentSwipePost = null;
                }, 300);
            }
        }

        function showPostSwipeActions(postElement) {
            // Create swipe actions if they don't exist
            let actionsContainer = postElement.querySelector('.post-swipe-actions');
            if (!actionsContainer) {
                actionsContainer = document.createElement('div');
                actionsContainer.className = 'post-swipe-actions';
                actionsContainer.innerHTML = `
                    <button class="swipe-action-btn" data-action="like">
                        <i data-lucide="heart" class="w-5 h-5"></i>
                    </button>
                    <button class="swipe-action-btn" data-action="share">
                        <i data-lucide="share" class="w-5 h-5"></i>
                    </button>
                    <button class="swipe-action-btn" data-action="bookmark">
                        <i data-lucide="bookmark" class="w-5 h-5"></i>
                    </button>
                `;

                // Make post container relative if not already
                if (!postElement.classList.contains('post-swipe-container')) {
                    postElement.classList.add('post-swipe-container');
                }

                postElement.appendChild(actionsContainer);
                lucide.createIcons();

                // Add action listeners
                actionsContainer.querySelectorAll('.swipe-action-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const action = btn.dataset.action;
                        handleSwipeAction(action, postElement);
                        hidePostSwipeActions(postElement);
                    });
                });
            }

            actionsContainer.classList.add('visible');

            // Auto-hide after 3 seconds
            setTimeout(() => {
                hidePostSwipeActions(postElement);
            }, 3000);
        }

        function hidePostSwipeActions(postElement) {
            const actionsContainer = postElement.querySelector('.post-swipe-actions');
            if (actionsContainer) {
                actionsContainer.classList.remove('visible');
            }
        }

        function triggerQuickLike(postElement) {
            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(30);
            }

            // Find and trigger the like button
            const likeBtn = postElement.querySelector('.like-btn');
            if (likeBtn) {
                likeBtn.click();

                // Visual feedback
                showQuickLikeAnimation(postElement);
            }
        }

        function showQuickLikeAnimation(postElement) {
            const heart = document.createElement('div');
            heart.innerHTML = '<i data-lucide="heart" class="w-8 h-8 text-red-500"></i>';
            heart.className = 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none z-20';
            heart.style.animation = 'quickLike 1s ease-out forwards';

            postElement.style.position = 'relative';
            postElement.appendChild(heart);
            lucide.createIcons();

            setTimeout(() => {
                heart.remove();
            }, 1000);
        }

        function handleSwipeAction(action, postElement) {
            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            switch(action) {
                case 'like':
                    const likeBtn = postElement.querySelector('.like-btn');
                    if (likeBtn) likeBtn.click();
                    break;
                case 'share':
                    const shareBtn = postElement.querySelector('.share-btn');
                    if (shareBtn) shareBtn.click();
                    break;
                case 'bookmark':
                    // Placeholder for bookmark functionality
                    showToast('Bookmarked!');
                    break;
            }
        }

        // Setup touch feedback
        function setupTouchFeedback() {
            // Add touch feedback to buttons and interactive elements
            const interactiveElements = document.querySelectorAll('button, .mobile-nav-item, article');

            interactiveElements.forEach(element => {
                if (!element.classList.contains('touch-feedback')) {
                    element.classList.add('touch-feedback');
                    element.addEventListener('touchstart', createRippleEffect, { passive: true });
                }
            });
        }

        function createRippleEffect(e) {
            const element = e.currentTarget;
            const rect = element.getBoundingClientRect();
            const ripple = document.createElement('span');
            const size = Math.max(rect.width, rect.height);
            const x = e.touches[0].clientX - rect.left - size / 2;
            const y = e.touches[0].clientY - rect.top - size / 2;

            ripple.className = 'touch-ripple';
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';

            element.appendChild(ripple);

            // Add haptic feedback for important actions
            if (element.classList.contains('mobile-nav-item') ||
                element.classList.contains('like-btn') ||
                element.classList.contains('comment-btn') ||
                element.type === 'submit') {
                if (navigator.vibrate) {
                    navigator.vibrate(25); // Light haptic feedback
                }
            }

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // Remove mobile gestures (for desktop)
        function removeMobileGestures() {
            // Remove event listeners and reset states
            isRefreshing = false;
            isSwipeActive = false;
            currentSwipePost = null;

            // Hide any visible swipe actions
            document.querySelectorAll('.post-swipe-actions.visible').forEach(actions => {
                actions.classList.remove('visible');
            });
        }

        // Toast notification system
        function showToast(message, duration = 2000) {
            const toast = document.createElement('div');
            toast.className = 'fixed bottom-24 left-1/2 transform -translate-x-1/2 bg-slate-800 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300';
            toast.textContent = message;
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(-50%) translateY(20px)';

            document.body.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(-50%) translateY(0)';
            }, 10);

            // Animate out and remove
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(-50%) translateY(20px)';
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        // Add quick like animation CSS
        const quickLikeStyle = document.createElement('style');
        quickLikeStyle.textContent = `
            @keyframes quickLike {
                0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
                50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
                100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
            }
        `;
        document.head.appendChild(quickLikeStyle);

        // --- Mobile Bottom Navigation ---
        const mobileBottomNav = document.getElementById('mobileBottomNav');
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
        const mobileNotificationBadge = document.getElementById('mobileNotificationBadge');

        // Initialize mobile navigation
        function initializeMobileNavigation() {
            // Show/hide mobile navigation based on authentication and view
            updateMobileNavVisibility();

            // Set up navigation event listeners
            mobileNavItems.forEach(item => {
                item.addEventListener('click', handleMobileNavClick);
            });
        }

        // Handle mobile navigation clicks
        function handleMobileNavClick(e) {
            const tab = e.currentTarget.getAttribute('data-tab');

            // Update active state
            mobileNavItems.forEach(item => item.classList.remove('active'));
            e.currentTarget.classList.add('active');

            // Handle navigation based on tab
            switch(tab) {
                case 'home':
                    // Scroll to top of feed or refresh
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                    break;

                case 'search':
                    // Open mobile search modal
                    document.getElementById('mobileSearchModal').classList.remove('hidden');
                    document.getElementById('mobileSearchInput').focus();
                    break;

                case 'create':
                    // Focus on story creation
                    requireAuth('createPost', () => {
                        const titleInput = document.getElementById('storyTitleInput');
                        if (titleInput) {
                            titleInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            titleInput.focus();
                        }
                    });
                    break;

                case 'notifications':
                    // Show notifications
                    requireAuth('viewNotifications', () => {
                        // For mobile, we'll create a full-screen notifications view
                        showMobileNotifications();
                    });
                    break;

                case 'profile':
                    // Show profile or auth modal
                    if (currentUser) {
                        showMobileProfile();
                    } else {
                        toggleAuthModal();
                    }
                    break;
            }
        }

        // Update mobile navigation visibility
        function updateMobileNavVisibility() {
            const isMainApp = !document.getElementById('mainApp').classList.contains('hidden');
            if (mobileBottomNav) {
                if (isMainApp) {
                    mobileBottomNav.classList.remove('hidden');
                } else {
                    mobileBottomNav.classList.add('hidden');
                }
            }
        }

        // Update mobile notification badge
        function updateMobileNotificationBadge(count) {
            if (mobileNotificationBadge) {
                if (count > 0) {
                    mobileNotificationBadge.textContent = count > 99 ? '99+' : count.toString();
                    mobileNotificationBadge.classList.remove('hidden');
                } else {
                    mobileNotificationBadge.classList.add('hidden');
                }
            }
        }

        // Show mobile notifications with full-screen experience
        function showMobileNotifications() {
            // Create mobile-optimized notifications modal
            const existingModal = document.getElementById('mobileNotificationsModal');
            if (existingModal) {
                existingModal.classList.remove('hidden');
                return;
            }

            const modal = document.createElement('div');
            modal.id = 'mobileNotificationsModal';
            modal.className = 'fixed inset-0 bg-white z-50 md:hidden';
            modal.innerHTML = `
                <div class="flex flex-col h-full">
                    <header class="bg-white border-b border-slate-200 p-4 flex items-center justify-between">
                        <h1 class="text-xl font-bold">Notifications</h1>
                        <button onclick="closeMobileNotifications()" class="p-2 hover:bg-slate-100 rounded-lg">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </header>
                    <div class="flex-1 overflow-y-auto p-4">
                        <div id="mobileNotificationsList">
                            <div class="text-center py-8">
                                <i data-lucide="bell" class="w-12 h-12 text-slate-300 mx-auto mb-4"></i>
                                <p class="text-slate-500">No notifications yet</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            lucide.createIcons();

            // Load notifications
            loadNotifications();
        }

        // Close mobile notifications
        window.closeMobileNotifications = function() {
            const modal = document.getElementById('mobileNotificationsModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        };

        // Show mobile profile with full-screen experience
        function showMobileProfile() {
            if (!currentUser) return;

            const existingModal = document.getElementById('mobileProfileModal');
            if (existingModal) {
                existingModal.classList.remove('hidden');
                return;
            }

            const modal = document.createElement('div');
            modal.id = 'mobileProfileModal';
            modal.className = 'fixed inset-0 bg-white z-50 md:hidden';
            modal.innerHTML = `
                <div class="flex flex-col h-full">
                    <header class="bg-white border-b border-slate-200 p-4 flex items-center justify-between">
                        <h1 class="text-xl font-bold">Profile</h1>
                        <button onclick="closeMobileProfile()" class="p-2 hover:bg-slate-100 rounded-lg">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </header>
                    <div class="flex-1 overflow-y-auto p-4">
                        <div class="text-center mb-6">
                            <img src="https://placehold.co/80x80/E2E8F0/475569?text=${currentUser.username.substring(0, 2).toUpperCase()}"
                                 alt="${currentUser.username}" class="w-20 h-20 rounded-full mx-auto mb-4">
                            <h2 class="text-xl font-bold">${currentUser.username}</h2>
                            <p class="text-slate-500">${currentUser.email}</p>
                        </div>
                        <div class="space-y-4">
                            <button onclick="editMobileProfile()" class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium">
                                Edit Profile
                            </button>
                            <button onclick="showMobileSettings()" class="w-full bg-slate-100 text-slate-700 py-3 rounded-lg font-medium">
                                Settings
                            </button>
                            <button onclick="confirmMobileLogout()" class="w-full bg-red-100 text-red-700 py-3 rounded-lg font-medium">
                                Sign Out
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            lucide.createIcons();
        }

        // Close mobile profile
        window.closeMobileProfile = function() {
            const modal = document.getElementById('mobileProfileModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        };

        // Mobile logout confirmation
        window.confirmMobileLogout = async function() {
            if (confirm('Are you sure you want to sign out?')) {
                try {
                    await window.FirebaseAuth.signOut();
                    closeMobileProfile();
                    showSuccessMessage('You have been signed out successfully.');
                } catch (error) {
                    console.error('Logout error:', error);
                    // Fallback to local logout
                    clearUserSession();
                    showLandingPage();
                    closeMobileProfile();
                    showSuccessMessage('You have been signed out successfully.');
                }
            }
        };

        // Initialize mobile navigation when DOM is ready
        initializeMobileNavigation();

        // Initialize mobile gestures
        initializeMobileGestures();
    </script>

    <!-- Mobile Bottom Navigation -->
    <nav id="mobileBottomNav" class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 z-30">
        <div class="flex justify-around items-center py-2">
            <!-- Home Tab -->
            <button class="mobile-nav-item active flex flex-col items-center py-2 px-3 rounded-lg" data-tab="home">
                <div class="nav-icon relative">
                    <i data-lucide="home" class="w-6 h-6"></i>
                </div>
                <span class="text-xs mt-1 font-medium">Home</span>
            </button>

            <!-- Search Tab -->
            <button class="mobile-nav-item flex flex-col items-center py-2 px-3 rounded-lg" data-tab="search">
                <div class="nav-icon relative">
                    <i data-lucide="search" class="w-6 h-6"></i>
                </div>
                <span class="text-xs mt-1 font-medium">Search</span>
            </button>

            <!-- Create Tab -->
            <button class="mobile-nav-item flex flex-col items-center py-2 px-3 rounded-lg" data-tab="create">
                <div class="nav-icon relative">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                </div>
                <span class="text-xs mt-1 font-medium">Create</span>
            </button>

            <!-- Notifications Tab -->
            <button class="mobile-nav-item flex flex-col items-center py-2 px-3 rounded-lg" data-tab="notifications">
                <div class="nav-icon relative">
                    <i data-lucide="bell" class="w-6 h-6"></i>
                    <span id="mobileNotificationBadge" class="mobile-nav-badge hidden">0</span>
                </div>
                <span class="text-xs mt-1 font-medium">Alerts</span>
            </button>

            <!-- Profile Tab -->
            <button class="mobile-nav-item flex flex-col items-center py-2 px-3 rounded-lg" data-tab="profile">
                <div class="nav-icon relative">
                    <i data-lucide="user" class="w-6 h-6"></i>
                </div>
                <span class="text-xs mt-1 font-medium">Profile</span>
            </button>
        </div>
    </nav>

    <script>
        // Initialize mobile interaction enhancements
        if (window.innerWidth <= 768) {
            addMobileInteractionEnhancements();
        }

        // Enhanced mobile interactions
        function addMobileInteractionEnhancements() {
            // Add long press support for additional actions
            let longPressTimer;
            let isLongPress = false;

            document.addEventListener('touchstart', (e) => {
                if (e.target.closest('article')) {
                    longPressTimer = setTimeout(() => {
                        isLongPress = true;
                        showPostContextMenu(e.target.closest('article'), e.touches[0]);
                    }, 500);
                }
            }, { passive: true });

            document.addEventListener('touchend', () => {
                clearTimeout(longPressTimer);
                setTimeout(() => { isLongPress = false; }, 100);
            }, { passive: true });

            document.addEventListener('touchmove', () => {
                clearTimeout(longPressTimer);
            }, { passive: true });
        }

        // Show context menu for posts on long press
        function showPostContextMenu(postElement, touch) {
            if (navigator.vibrate) {
                navigator.vibrate(50); // Medium haptic feedback for context menu
            }

            const contextMenu = document.createElement('div');
            contextMenu.className = 'fixed bg-white rounded-lg shadow-lg border border-slate-200 z-50 p-2';
            contextMenu.style.left = Math.min(touch.clientX, window.innerWidth - 200) + 'px';
            contextMenu.style.top = Math.min(touch.clientY, window.innerHeight - 150) + 'px';

            contextMenu.innerHTML = `
                <button class="w-full text-left px-4 py-3 hover:bg-slate-50 rounded flex items-center gap-3" onclick="handleContextAction('share', this)">
                    <i data-lucide="share" class="w-4 h-4"></i>
                    Share Story
                </button>
                <button class="w-full text-left px-4 py-3 hover:bg-slate-50 rounded flex items-center gap-3" onclick="handleContextAction('bookmark', this)">
                    <i data-lucide="bookmark" class="w-4 h-4"></i>
                    Bookmark
                </button>
                <button class="w-full text-left px-4 py-3 hover:bg-slate-50 rounded flex items-center gap-3" onclick="handleContextAction('report', this)">
                    <i data-lucide="flag" class="w-4 h-4"></i>
                    Report
                </button>
            `;

            document.body.appendChild(contextMenu);
            lucide.createIcons();

            // Remove context menu when clicking outside
            setTimeout(() => {
                document.addEventListener('click', function removeContextMenu() {
                    contextMenu.remove();
                    document.removeEventListener('click', removeContextMenu);
                });
            }, 100);
        }

        // Handle context menu actions
        window.handleContextAction = function(action, element) {
            element.closest('.fixed').remove();

            switch(action) {
                case 'share':
                    if (navigator.share) {
                        navigator.share({
                            title: 'Check out this story on Naroop',
                            url: window.location.href
                        });
                    } else {
                        // Fallback for browsers without Web Share API
                        navigator.clipboard.writeText(window.location.href);
                        showSuccessMessage('Link copied to clipboard!');
                    }
                    break;
                case 'bookmark':
                    showSuccessMessage('Story bookmarked!');
                    break;
                case 'report':
                    if (confirm('Report this story for inappropriate content?')) {
                        showSuccessMessage('Story reported. Thank you for helping keep our community safe.');
                    }
                    break;
            }
        };
    </script>
</body>
</html>
