// Firebase Configuration and Authentication
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { 
    getAuth, 
    createUserWithEmailAndPassword, 
    signInWithEmailAndPassword, 
    signOut, 
    onAuthStateChanged,
    updateProfile
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Firebase configuration - these values will be loaded from the server
let firebaseConfig = {};

// Firebase app and auth instances
let app;
let auth;

// Initialize Firebase
async function initializeFirebase() {
    try {
        // Fetch Firebase config from server
        const response = await fetch('/api/firebase-config');
        if (!response.ok) {
            throw new Error('Failed to fetch Firebase configuration');
        }
        
        firebaseConfig = await response.json();
        
        // Initialize Firebase
        app = initializeApp(firebaseConfig);
        auth = getAuth(app);
        
        console.log('Firebase initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing Firebase:', error);
        return false;
    }
}

// Authentication functions
export const FirebaseAuth = {
    // Initialize Firebase
    async init() {
        return await initializeFirebase();
    },

    // Get current user
    getCurrentUser() {
        return auth?.currentUser || null;
    },

    // Sign up with email and password
    async signUp(email, password, username) {
        try {
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Update user profile with username
            await updateProfile(user, {
                displayName: username
            });
            
            return {
                success: true,
                user: {
                    uid: user.uid,
                    email: user.email,
                    username: username,
                    displayName: user.displayName
                }
            };
        } catch (error) {
            console.error('Sign up error:', error);
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign in with email and password
    async signIn(email, password) {
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            return {
                success: true,
                user: {
                    uid: user.uid,
                    email: user.email,
                    username: user.displayName || user.email.split('@')[0],
                    displayName: user.displayName
                }
            };
        } catch (error) {
            console.error('Sign in error:', error);
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign out
    async signOut() {
        try {
            await signOut(auth);
            return { success: true };
        } catch (error) {
            console.error('Sign out error:', error);
            return {
                success: false,
                error: 'Failed to sign out'
            };
        }
    },

    // Listen for authentication state changes
    onAuthStateChanged(callback) {
        if (!auth) {
            console.error('Firebase auth not initialized');
            return () => {};
        }
        
        return onAuthStateChanged(auth, (user) => {
            if (user) {
                callback({
                    uid: user.uid,
                    email: user.email,
                    username: user.displayName || user.email.split('@')[0],
                    displayName: user.displayName
                });
            } else {
                callback(null);
            }
        });
    },

    // Get Firebase ID token for server authentication
    async getIdToken() {
        try {
            const user = auth?.currentUser;
            if (!user) {
                throw new Error('No authenticated user');
            }
            
            return await user.getIdToken();
        } catch (error) {
            console.error('Error getting ID token:', error);
            return null;
        }
    }
};

// Helper function to convert Firebase error codes to user-friendly messages
function getFirebaseErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/user-not-found':
            return 'No account found with this email address.';
        case 'auth/wrong-password':
            return 'Incorrect password. Please try again.';
        case 'auth/email-already-in-use':
            return 'An account with this email already exists.';
        case 'auth/weak-password':
            return 'Password should be at least 6 characters long.';
        case 'auth/invalid-email':
            return 'Please enter a valid email address.';
        case 'auth/too-many-requests':
            return 'Too many failed attempts. Please try again later.';
        case 'auth/network-request-failed':
            return 'Network error. Please check your connection.';
        default:
            return 'An error occurred. Please try again.';
    }
}

// Export for global access
window.FirebaseAuth = FirebaseAuth;
